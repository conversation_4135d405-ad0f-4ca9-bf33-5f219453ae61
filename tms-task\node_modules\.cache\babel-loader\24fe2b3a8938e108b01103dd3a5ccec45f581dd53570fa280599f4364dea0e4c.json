{"ast": null, "code": "var _jsxFileName = \"F:\\\\Ficent\\\\work\\\\tms_git\\\\tms_dhx_tms_frontend\\\\tms-task\\\\src\\\\common\\\\components\\\\ContextBoard.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { CheckOutlined, RightOutlined } from '@ant-design/icons';\nimport { team_036_get_node_ctx_options } from \"@common/api/http\";\nimport AppNodeResourceIcon from \"@components/AppNodeResourceIcon\";\nimport { useQuerySetting327_getTeamSpaceAadmin, useQuerySetting407_getCodeValueList } from \"@common/service/commonHooks\";\nimport { isEmpty, treeToArray, getSysIconList, assembleGroup } from \"@common/utils/ArrayUtils\";\nimport { eCtxOptionType, eEnableFlg, eProductId } from \"@common/utils/enum\";\nimport { eCtxTypeId, eMenuStatus, getCtxIconByType, getMenuStatusIcon, eNameTextFontType } from \"@common/utils/TsbConfig\";\nimport { expiredModal, resourceMaxModal, unVipModal } from \"@common/utils/ViewUtils\";\nimport { Checkbox, Space, Skeleton, Modal } from \"antd\";\nimport React, { forwardRef, useImperativeHandle, useRef, useState, useEffect } from \"react\";\nimport { Item as CtxMenuItem, Menu as CtxMenu, Submenu as CtxSubMenu, useContextMenu, Separator } from \"react-contexify\";\nimport { formatSvg } from \"@common/utils/ViewUtils\";\nimport \"react-contexify/ReactContexify.css\";\nimport { useMutation } from '@tanstack/react-query';\nimport \"./ContextBoard.scss\";\nimport * as https from \"@common/api/http\";\nimport { setting_320_get_node_priv_query } from \"@common/api/query/query_setting\";\nimport { globalUtil } from \"@common/utils/globalUtil\";\n\n// 图标颜色\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction ColorSelectedMenu({\n  selectValue,\n  onChange,\n  colorOptionsList,\n  actionType\n}) {\n  const _onChanged = checkedValue => {\n    let value = checkedValue.length ? checkedValue[checkedValue.length - 1] : \"\"; // 选中/取消选中 多选，由数组存储，选中后，前一个选中不会自动取消，因此要取最后一个最新选中的值\n    onChange && onChange({\n      id: actionType + \"&\" + value\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: \"inline-block\"\n    },\n    children: /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n      value: [selectValue],\n      onChange: _onChanged,\n      children: colorOptionsList.map(item => /*#__PURE__*/_jsxDEV(Checkbox, {\n        name: \"xxx\",\n        value: item.type.toString(),\n        className: item.className\n      }, item.type.toString(), false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 37\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 10\n  }, this);\n}\n\n// 标题颜色\n_c = ColorSelectedMenu;\nfunction TitleSelectedMenu({\n  selectValue,\n  onChange,\n  colorOptionsList,\n  actionType\n}) {\n  const _onChanged = checkedValue => {\n    let value = checkedValue.length ? checkedValue[checkedValue.length - 1] : \"\"; // 选中/取消选中 多选，由数组存储，选中后，前一个选中不会自动取消，因此要取最后一个最新选中的值\n    onChange && onChange({\n      id: actionType + \"&\" + value\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: \"inline-block\"\n    },\n    children: /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n      value: [selectValue],\n      onChange: _onChanged,\n      children: colorOptionsList.map(item => /*#__PURE__*/_jsxDEV(Checkbox, {\n        name: \"xxx\",\n        value: item.type.toString(),\n        className: item.className,\n        children: selectValue == item.type ? \"\" : item.title\n      }, item.type.toString(), false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 37\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 10\n  }, this);\n}\n\n// 字体个性化设置，前段需要固定识别，存储的是type值\n_c2 = TitleSelectedMenu;\nfunction FontSelectedMenu({\n  onChange,\n  optionList,\n  actionType\n}) {\n  const _onChanged = checkedValue => {\n    onChange && onChange({\n      id: actionType + \"&\" + checkedValue\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Space, {\n    size: 10,\n    children: optionList.map(option => /*#__PURE__*/_jsxDEV(\"div\", {\n      onClick: () => _onChanged(option === null || option === void 0 ? void 0 : option.type),\n      className: option.className,\n      children: formatSvg(option.icon)\n    }, option === null || option === void 0 ? void 0 : option.type, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 10\n  }, this);\n}\n\n// 图标设置，无需固定，有多少显示多少，存储的是value值\n_c3 = FontSelectedMenu;\nfunction IconSelectedMenu({\n  onChange,\n  optionList,\n  actionType\n}) {\n  const _onChanged = checkedValue => {\n    onChange && onChange({\n      id: actionType + \"&\" + checkedValue\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Space, {\n    size: 10,\n    children: optionList.map(option => /*#__PURE__*/_jsxDEV(\"div\", {\n      onClick: () => _onChanged(option === null || option === void 0 ? void 0 : option.value),\n      className: option.className,\n      children: formatSvg(option.icon)\n    }, option === null || option === void 0 ? void 0 : option.type, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 10\n  }, this);\n}\n\n/**\r\n * @description 文档图标\r\n */\n/*function TitleIconSelectedMenu({ selectValue, onChange, colorOptionsList }) {\r\n  const _onChanged = (checkedValue) => {\r\n    let value = checkedValue.length ? checkedValue[checkedValue.length - 1] : \"\" // 选中/取消选中 多选，由数组存储，选中后，前一个选中不会自动取消，因此要取最后一个最新选中的值\r\n    onChange && onChange({ id: eCtxTypeId.ctx_42_set_icon + \"&\" + value })\r\n  }\r\n  return <div style={{ display: \"inline-block\" }}>\r\n  <Checkbox.Group value={[selectValue]} onChange={_onChanged}>\r\n    {colorOptionsList.map(option => (\r\n      <Checkbox\r\n        key={option.value?.toString()} \r\n        value={option.value?.toString()}\r\n        className=\"checkbox-bage\"\r\n      >\r\n        <div className=\"checkbox-bage-icon\">\r\n          {formatSvg(option.icon)}\r\n        </div>\r\n      </Checkbox>\r\n    ))}\r\n  </Checkbox.Group>\r\n</div>\r\n}*/\n\n/*// checkbox 选中状态\r\nconst getCheckboxItem = (flag, label, color, className, actionType) => {\r\n  return <div style={{ display: \"flex\", justifyContent: \"space-between\", alignItems: \"baseline\" }}>\r\n    {/!* tmsbug-2622：删除线文案本身添加删除线 *!/}\r\n    <span style={{ color: color }} className={`${actionType == eCtxTypeId.ctx_46_text_font ? \"tree-dir-title-delete\" : \"\"}`}>{label}</span>\r\n    <Checkbox checked={flag} className={className} />\r\n  </div>\r\n}*/\n\n// 收藏选中状态\n_c4 = IconSelectedMenu;\nconst getCheckItem = (flag, label, color) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      color\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 5\n    }, this), flag ? /*#__PURE__*/_jsxDEV(CheckOutlined, {\n      style: {\n        color: \"#70b603\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 13\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 10\n  }, this);\n};\n\n// 自定义延迟子菜单组件\nconst DelayedCtxSubMenu = ({\n  children,\n  label,\n  arrow,\n  ...props\n}) => {\n  _s();\n  const timeoutRef = useRef(null);\n  const [isVisible, setIsVisible] = useState(false);\n  const menuRef = useRef(null);\n  const subMenuRef = useRef(null);\n  const isMouseInMenu = useRef(false);\n  const [subMenuPosition, setSubMenuPosition] = useState({\n    top: 0,\n    left: '100%'\n  });\n  const [subMenuHeight, setSubMenuHeight] = useState(0);\n\n  // 计算预估的子菜单高度\n  const calculateEstimatedHeight = () => {\n    // 获取子菜单项的数量\n    const menuItems = React.Children.toArray(children);\n    let itemCount = 0;\n\n    // 递归计算所有菜单项（包括分组内的项）\n    const countItems = items => {\n      items.forEach(item => {\n        if (/*#__PURE__*/React.isValidElement(item)) {\n          if (item.props && item.props.children) {\n            if (Array.isArray(item.props.children)) {\n              countItems(item.props.children);\n            } else {\n              itemCount++;\n            }\n          } else {\n            itemCount++;\n          }\n        }\n      });\n    };\n    countItems(menuItems);\n\n    // 单个菜单项的高度（包括padding和border）\n    const ITEM_HEIGHT = 32; // 增加高度估算，考虑到实际渲染可能更高\n    // 子菜单的padding和边距\n    const MENU_PADDING = 16; // 增加padding估算\n\n    return Math.min(itemCount * ITEM_HEIGHT + MENU_PADDING, window.innerHeight - 40); // 限制最大高度\n  };\n\n  // 添加 ResizeObserver 监听子菜单高度变化\n  useEffect(() => {\n    if (!subMenuRef.current) return;\n    const resizeObserver = new ResizeObserver(entries => {\n      for (let entry of entries) {\n        setSubMenuHeight(entry.contentRect.height);\n      }\n    });\n    resizeObserver.observe(subMenuRef.current);\n    return () => {\n      resizeObserver.disconnect();\n    };\n  }, [isVisible]);\n  const showSubMenu = () => {\n    isMouseInMenu.current = true;\n    if (timeoutRef.current) {\n      clearTimeout(timeoutRef.current);\n    }\n    setIsVisible(true);\n\n    // 延迟计算位置，确保子菜单已经渲染到DOM中\n    setTimeout(() => {\n      if (menuRef.current && subMenuRef.current) {\n        const menuRect = menuRef.current.getBoundingClientRect();\n        const subMenuRect = subMenuRef.current.getBoundingClientRect();\n        const windowHeight = window.innerHeight;\n        const windowWidth = window.innerWidth;\n\n        // 获取实际的子菜单高度，如果还没有渲染完成则使用估算高度\n        const actualHeight = subMenuRect.height > 0 ? subMenuRect.height : calculateEstimatedHeight();\n        const subMenuWidth = subMenuRect.width > 0 ? subMenuRect.width : 200; // 默认宽度\n\n        // 默认与父菜单项顶部对齐，并往上偏移50px\n        let topPosition = -50;\n        let leftPosition = '100%';\n\n        // 检查子菜单底部是否会超出屏幕\n        const bottomOverflow = menuRect.top + actualHeight - windowHeight;\n        if (bottomOverflow > 0) {\n          // 如果会超出屏幕底部，向上调整位置\n          topPosition = Math.max(-menuRect.top + 10, -bottomOverflow - 60); // 至少留10px的边距\n        }\n\n        // 检查子菜单右侧是否会超出屏幕\n        const rightOverflow = menuRect.right + subMenuWidth - windowWidth;\n        if (rightOverflow > 0) {\n          // 如果会超出屏幕右侧，显示在左侧\n          leftPosition = `-${subMenuWidth}px`;\n          // 添加CSS类标识左侧定位\n          if (subMenuRef.current) {\n            subMenuRef.current.classList.add('submenu-left-positioned');\n          }\n        } else {\n          // 移除左侧定位类\n          if (subMenuRef.current) {\n            subMenuRef.current.classList.remove('submenu-left-positioned');\n          }\n        }\n        setSubMenuPosition({\n          top: topPosition,\n          left: leftPosition\n        });\n      }\n    }, 10); // 给一个小的延迟确保DOM更新\n  };\n  const hideSubMenu = () => {\n    if (timeoutRef.current) {\n      clearTimeout(timeoutRef.current);\n    }\n    timeoutRef.current = setTimeout(() => {\n      if (!isMouseInMenu.current) {\n        setIsVisible(false);\n      }\n    }, 1000);\n  };\n\n  // 检查是否是有效的DOM节点，解决截图时焦点失去报错问题；\n  const isValidNode = node => {\n    try {\n      return node && node instanceof Node;\n    } catch (e) {\n      return false;\n    }\n  };\n  const handleMouseEnter = () => {\n    showSubMenu();\n  };\n  const handleMouseLeave = e => {\n    var _menuRef$current;\n    // 检查鼠标是否移动到子菜单\n    if (subMenuRef.current && isValidNode(e.relatedTarget) && subMenuRef.current.contains(e.relatedTarget)) {\n      return;\n    }\n\n    // 检查鼠标是否移动到其他父菜单\n    const parentMenu = (_menuRef$current = menuRef.current) === null || _menuRef$current === void 0 ? void 0 : _menuRef$current.parentElement;\n    if (parentMenu && isValidNode(e.relatedTarget) && parentMenu.contains(e.relatedTarget)) {\n      return;\n    }\n    isMouseInMenu.current = false;\n    hideSubMenu();\n  };\n  const handleSubMenuMouseEnter = () => {\n    showSubMenu();\n  };\n  const handleSubMenuMouseLeave = e => {\n    var _menuRef$current2;\n    // 检查鼠标是否移动到主菜单\n    if (menuRef.current && isValidNode(e.relatedTarget) && menuRef.current.contains(e.relatedTarget)) {\n      return;\n    }\n\n    // 检查鼠标是否移动到其他父菜单\n    const parentMenu = (_menuRef$current2 = menuRef.current) === null || _menuRef$current2 === void 0 ? void 0 : _menuRef$current2.parentElement;\n    if (parentMenu && isValidNode(e.relatedTarget) && parentMenu.contains(e.relatedTarget)) {\n      return;\n    }\n    isMouseInMenu.current = false;\n    hideSubMenu();\n  };\n  React.useEffect(() => {\n    return () => {\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current);\n      }\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: menuRef,\n    onMouseEnter: handleMouseEnter,\n    onMouseLeave: handleMouseLeave,\n    className: \"delayed-submenu-container\",\n    style: {\n      position: 'relative'\n    },\n    children: /*#__PURE__*/_jsxDEV(CtxSubMenu, {\n      ...props,\n      label: label,\n      arrow: arrow,\n      style: {\n        ...props.style,\n        position: 'absolute',\n        left: subMenuPosition.left,\n        top: subMenuPosition.top,\n        zIndex: 999,\n        display: isVisible ? 'block' : 'none',\n        maxHeight: 'calc(100vh - 20px)',\n        // 限制最大高度\n        overflowY: 'auto' // 如果内容过多，允许滚动\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: subMenuRef,\n        onMouseEnter: handleSubMenuMouseEnter,\n        onMouseLeave: handleSubMenuMouseLeave,\n        style: {\n          width: '100%',\n          height: '100%',\n          position: 'relative',\n          zIndex: 1000\n        },\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 302,\n    columnNumber: 5\n  }, this);\n};\n\n/**\r\n * 右击菜单 \r\n * @param teamId 团队Id\r\n * @param onMoreBtnClick 非新建操作回调\r\n * @param onCreateBtnClick 新建等操作回调\r\n * @param id 菜单id\r\n * @param callbackParams 是 onMoreBtnClick 和 onCreateBtnClick 回调返回参数\r\n */\n_s(DelayedCtxSubMenu, \"SXfbv8zj7IOi6Tr2bBOduNvfROc=\");\n_c5 = DelayedCtxSubMenu;\nfunction ContextBoard({\n  teamId,\n  onMoreBtnClick,\n  onCreateBtnClick,\n  id,\n  handleOnVisibilityChange,\n  ...callbackParams\n}, ref) {\n  _s2();\n  // const [createTypeList, setCreateTypeList] = useState([]); // tree right click context menu\n  // const [nodeData,setNodeData] = useState(null); \n  const {\n    data: {\n      userId,\n      teamAdminFlag: managerFlag\n    } = {\n      userId: undefined,\n      teamAdminFlag: undefined\n    }\n  } = useQuerySetting327_getTeamSpaceAadmin({\n    teamId,\n    enabled: true\n  }); // 判断登录人员是否是团队管理员\n  const {\n    data: selectionList\n  } = useQuerySetting407_getCodeValueList(teamId); //字典数据\n\n  const {\n    isLoading: isCtxLoading,\n    data: createTypeList = [],\n    mutateAsync\n  } = useMutation({\n    mutationFn: ({\n      nodeData,\n      nodeId,\n      filterActionTypes,\n      ctxOptionList,\n      nodeType\n    }) => loadContextMenuList(nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType)\n  });\n  const setting334Mutation = useMutation({\n    mutationFn: https.setting_334_apply_authorization\n  });\n\n  //产品申请开通授权\n  const applyProductAuthorize = productId => {\n    if (!!productId) {\n      setting334Mutation.mutate({\n        teamId,\n        productId\n      }, {\n        onSuccess: result => {\n          if (result.resultCode === 200) {\n            //globalUtil.success(\"提交申请成功！\");\n            Modal.info({\n              title: \"提示\",\n              content: \"提交成功，管理员会接收到申请信息，请您耐心等候\",\n              maskClosable: true,\n              //centered: true, // 居中\n              okText: \"我知道了\",\n              width: 500\n            });\n          }\n        }\n      });\n    }\n  };\n  const cacheRef = useRef({});\n  const {\n    show\n  } = useContextMenu({\n    id: id\n  });\n  useImperativeHandle(ref, () => ({\n    /// const nodeData = {\n    ///   nodeId,\n    ///   rightFlgIconType： 图标颜色\n    ///   nameTextColorType：名称颜色\n    ///   nameTextStrikeFlg：删除线\n    /// }\n    showContextBoard: (e, nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType) => _onShow(e, nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType)\n  }));\n\n  // load current node menu context and show\n  // ctxOptionList：自定义传入ctxOptionList，无需接口获取\n  const _onShow = async (e, nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType) => {\n    // 计算菜单位置\n    const menuHeight = 300; // 预估菜单高度\n    const menuWidth = 200; // 预估菜单宽度\n    const windowHeight = window.innerHeight;\n    const windowWidth = window.innerWidth;\n    const clickY = e.clientY;\n    const clickX = e.clientX;\n\n    // 如果点击位置靠近底部，向上偏移\n    const adjustedY = clickY + menuHeight > windowHeight ? Math.max(0, windowHeight - menuHeight - 10) : clickY;\n\n    // 如果点击位置靠近右侧，向左偏移\n    const adjustedX = clickX + menuWidth > windowWidth ? Math.max(0, windowWidth - menuWidth - 10) : clickX;\n\n    // 使用 react-contexify 的默认定位机制，但添加位置调整\n    show({\n      event: e,\n      props: nodeData,\n      position: {\n        x: adjustedX,\n        y: adjustedY\n      }\n    });\n    mutateAsync({\n      nodeData,\n      nodeId,\n      filterActionTypes,\n      ctxOptionList,\n      nodeType\n    });\n    cacheRef.current.nodeData = nodeData;\n  };\n\n  // 特殊逻辑:1.由于报告模块同一份报告针对不同的人是不同类型的信件，根据nodeId无法区分出是发件箱,收件箱还是垃圾箱，所以需要前端传参nodeType来区分右击菜单\n  // tmsbug-8830: 新建接口定义的菜单项位置调整，已经如果已匹配过，则不需要呈现\n  const loadContextMenuList = async (nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType) => {\n    let result = !isEmpty(ctxOptionList) ? {\n      resultCode: 200,\n      ctxOptionList\n    } : await team_036_get_node_ctx_options({\n      teamId,\n      nodeId,\n      nodeType\n    });\n    if (result.resultCode == 200) {\n      let {\n        ctxOptionList,\n        favoriteFlg\n      } = result;\n      if (!isEmpty(filterActionTypes)) ctxOptionList = ctxOptionList.filter(ctxOption => !(filterActionTypes || []).some(actionType => ctxOption.actionType == actionType)); // 无需新建\n      ctxOptionList.forEach(ctxOption => {\n        switch (+ctxOption.actionType) {\n          case eCtxTypeId.ctx_38_create:\n            // 新建操作\n            ctxOption.children = assembleCreateTypeList(ctxOption.children);\n            break;\n          case eCtxTypeId.ctx_39_personalization:\n            // 个性化设置\n            ctxOption.children = assemblePersonalizationList(ctxOption.children, nodeData);\n            break;\n          case eCtxTypeId.ctx_60_flag_mail:\n            // 标记邮件\n            ctxOption.children = flagMailList(ctxOption.children, nodeData);\n            break;\n          case eCtxTypeId.ctx_37_favorite:\n            // 收藏\n            const favoriteFlag = favoriteFlg == eEnableFlg.enable;\n            ctxOption.colorType = favoriteFlag ? eEnableFlg.disable : eEnableFlg.enable;\n            ctxOption.name = getCheckItem(favoriteFlag, ctxOption.name);\n            break;\n          case eCtxTypeId.ctx_40_top:\n            // 置顶（报告独有）\n            const topFlag = nodeData.topFlg == eEnableFlg.enable;\n            ctxOption.colorType = topFlag ? eEnableFlg.disable : eEnableFlg.enable;\n            ctxOption.name = getCheckItem(topFlag, ctxOption.name);\n            break;\n          case eCtxTypeId.ctx_41_read_op:\n            // 已读/未读（报告独有）\n            const readFlag = nodeData.readFlg == eEnableFlg.enable;\n            ctxOption.colorType = readFlag ? eEnableFlg.disable : eEnableFlg.enable;\n            ctxOption.name = getCheckItem(readFlag, ctxOption.name);\n            break;\n          default:\n            ctxOption.children = ctxOption.children.map(_sub => ({\n              ..._sub,\n              actionType: ctxOption.actionType,\n              colorType: _sub.actionType\n            }));\n            break;\n        }\n      });\n      return ctxOptionList;\n    } else {\n      return [];\n    }\n  };\n\n  // 给新建菜单增加分组\n  const assembleCreateTypeList = createTypeList => {\n    let _createTypeList = assembleGroup(createTypeList, false);\n    _createTypeList = treeToArray(_createTypeList);\n    _createTypeList.forEach(_createType => {\n      _createType.colorType = _createType.actionType;\n      _createType.actionType = eCtxTypeId.ctx_38_create;\n    });\n    return _createTypeList;\n  };\n\n  // 个性化设置\n  const assemblePersonalizationList = (personalizationList, nodeData) => {\n    let _personalizationList = assembleGroup(personalizationList, true);\n    _personalizationList = _personalizationList.map(_personalization => {\n      if (_personalization.actionType == eCtxTypeId.ctx_18_set_figure_tag) {\n        var _nodeData$rightFlgIco;\n        // 图标颜色\n        const iconColorOptions = (_personalization.children || []).map(_child => {\n          try {\n            return JSON.parse(_child.name);\n          } catch (error) {\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name);\n            return {};\n          }\n        });\n        _personalization.children = [{\n          actionType: eCtxTypeId.ctx_18_set_figure_tag,\n          name: /*#__PURE__*/_jsxDEV(ColorSelectedMenu, {\n            selectValue: (_nodeData$rightFlgIco = nodeData.rightFlgIconType) === null || _nodeData$rightFlgIco === void 0 ? void 0 : _nodeData$rightFlgIco.toString(),\n            onChange: onClick,\n            colorOptionsList: iconColorOptions,\n            actionType: eCtxTypeId.ctx_18_set_figure_tag\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 19\n          }, this)\n        }];\n        return _personalization;\n      } else if (_personalization.actionType == eCtxTypeId.ctx_3_color_txt) {\n        var _nodeData$nameTextCol;\n        // 标题颜色\n        const textColorOptions = (_personalization.children || []).map(_child => {\n          try {\n            let option = JSON.parse(_child.name);\n            return option;\n          } catch (error) {\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name);\n            return {};\n          }\n        });\n        /*  _personalization.children = textColorOptions.map((option, index) => {\r\n           let flag = option.type == nodeData.nameTextColorType;\r\n           return {\r\n             actionType: eCtxTypeId.ctx_3_color_txt,\r\n             colorType: flag ? 0 : option.type,\r\n             name: getCheckboxItem(flag, option.title, option.value, option.className, eCtxTypeId.ctx_3_color_txt),\r\n           }\r\n         }); */\n        console.log(\"colorOptionsList\", textColorOptions);\n        _personalization.children = [{\n          actionType: eCtxTypeId.ctx_3_color_txt,\n          name: /*#__PURE__*/_jsxDEV(TitleSelectedMenu, {\n            selectValue: (_nodeData$nameTextCol = nodeData.nameTextColorType) === null || _nodeData$nameTextCol === void 0 ? void 0 : _nodeData$nameTextCol.toString(),\n            onChange: onClick,\n            colorOptionsList: textColorOptions,\n            actionType: eCtxTypeId.ctx_3_color_txt\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 19\n          }, this)\n        }];\n        return _personalization;\n      } else if (_personalization.actionType == eCtxTypeId.ctx_46_text_font) {\n        var _nodeData$nodeIconTyp;\n        // 设置字体个性化\n        let textFontTypeOptions = (_personalization.children || []).map(_child => {\n          try {\n            var _sysIconList$find, _nodeData$nameTextFon;\n            let option = JSON.parse(_child.name);\n            let sysIconList = getSysIconList(selectionList);\n            option.icon = (_sysIconList$find = sysIconList.find(sys => sys.propType == option.value)) === null || _sysIconList$find === void 0 ? void 0 : _sysIconList$find.propValue;\n            const nameTextFontTypeList = ((_nodeData$nameTextFon = nodeData.nameTextFontType) === null || _nodeData$nameTextFon === void 0 ? void 0 : _nodeData$nameTextFon.split(\",\")) || []; // 字体字段\n            option.className = nameTextFontTypeList[eNameTextFontType[option.type].idx] == eEnableFlg.enable ? `checked-gray-box` : \"\";\n            return option;\n          } catch (error) {\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name);\n            return {};\n          }\n        });\n        console.log(\"textFontTypeOptions\", textFontTypeOptions);\n        _personalization.children = [{\n          actionType: _personalization.actionType,\n          name: /*#__PURE__*/_jsxDEV(FontSelectedMenu, {\n            selectValue: (_nodeData$nodeIconTyp = nodeData.nodeIconType) === null || _nodeData$nodeIconTyp === void 0 ? void 0 : _nodeData$nodeIconTyp.toString(),\n            onChange: onClick,\n            optionList: textFontTypeOptions,\n            actionType: _personalization.actionType\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 19\n          }, this)\n        }];\n        return _personalization;\n      } else if (_personalization.actionType == eCtxTypeId.ctx_42_set_icon) {\n        var _nodeData$nodeIconTyp3;\n        // 设置图标\n        let titleIconOptions = (_personalization.children || []).map(_child => {\n          try {\n            var _sysIconList$find2, _nodeData$nodeIconTyp2;\n            let option = JSON.parse(_child.name);\n            let sysIconList = getSysIconList(selectionList);\n            option.icon = (_sysIconList$find2 = sysIconList.find(sys => sys.propType == option.value)) === null || _sysIconList$find2 === void 0 ? void 0 : _sysIconList$find2.propValue;\n            option.className = (((_nodeData$nodeIconTyp2 = nodeData.nodeIconType) === null || _nodeData$nodeIconTyp2 === void 0 ? void 0 : _nodeData$nodeIconTyp2.split(\",\")) || []).some(nodeIcon => option.value == nodeIcon) ? `checked-gray-box` : \"\";\n            return option;\n          } catch (error) {\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name);\n            return {};\n          }\n        });\n        _personalization.children = [{\n          actionType: _personalization.actionType,\n          name: /*#__PURE__*/_jsxDEV(IconSelectedMenu, {\n            selectValue: (_nodeData$nodeIconTyp3 = nodeData.nodeIconType) === null || _nodeData$nodeIconTyp3 === void 0 ? void 0 : _nodeData$nodeIconTyp3.toString(),\n            onChange: onClick,\n            optionList: titleIconOptions,\n            actionType: _personalization.actionType\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 19\n          }, this)\n        }];\n        return _personalization;\n      } else {\n        return _personalization;\n      }\n    });\n    _personalizationList = treeToArray(_personalizationList);\n    return _personalizationList;\n  };\n\n  // 标记邮件\n  const flagMailList = (personalizationList, nodeData) => {\n    let _personalizationList = assembleGroup(personalizationList, true);\n    _personalizationList = _personalizationList.map(_personalization => {\n      if (_personalization.actionType == eCtxTypeId.ctx_61_flag_color) {\n        var _nodeData$tagColor;\n        // 标记颜色\n        const iconColorOptions = (_personalization.children || []).map(_child => {\n          try {\n            return JSON.parse(_child.name);\n          } catch (error) {\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name);\n            return {};\n          }\n        });\n        _personalization.children = [{\n          actionType: _personalization.actionType,\n          name: /*#__PURE__*/_jsxDEV(ColorSelectedMenu, {\n            selectValue: (_nodeData$tagColor = nodeData.tagColor) === null || _nodeData$tagColor === void 0 ? void 0 : _nodeData$tagColor.toString(),\n            onChange: onClick,\n            colorOptionsList: iconColorOptions,\n            actionType: _personalization.actionType\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 19\n          }, this)\n        }];\n        return _personalization;\n      } else if (_personalization.actionType == eCtxTypeId.ctx_62_flag_img) {\n        var _nodeData$tagColor3;\n        // 标记图标\n        let titleIconOptions = (_personalization.children || []).map(_child => {\n          try {\n            var _getSysIconList$find, _nodeData$tagColor2;\n            let option = JSON.parse(_child.name);\n            option.icon = (_getSysIconList$find = getSysIconList(selectionList).find(sys => sys.propType == option.value)) === null || _getSysIconList$find === void 0 ? void 0 : _getSysIconList$find.propValue;\n            option.className = (((_nodeData$tagColor2 = nodeData.tagColor) === null || _nodeData$tagColor2 === void 0 ? void 0 : _nodeData$tagColor2.split(\",\")) || []).some(nodeIcon => option.value == nodeIcon) ? `checked-gray-box` : \"\";\n            return option;\n          } catch (error) {\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name);\n            return {};\n          }\n        });\n        _personalization.children = [{\n          actionType: _personalization.actionType,\n          name: /*#__PURE__*/_jsxDEV(IconSelectedMenu, {\n            selectValue: (_nodeData$tagColor3 = nodeData.tagColor) === null || _nodeData$tagColor3 === void 0 ? void 0 : _nodeData$tagColor3.toString(),\n            onChange: onClick,\n            optionList: titleIconOptions,\n            actionType: _personalization.actionType\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 19\n          }, this)\n        }];\n        return _personalization;\n      } else {\n        return _personalization;\n      }\n    });\n    _personalizationList = _personalizationList.flatMap(taskGroup => taskGroup.children);\n    return _personalizationList;\n  };\n\n  // menu item click\n  const onClick = ({\n    id,\n    props,\n    data,\n    triggerEvent,\n    ...args\n  }) => {\n    let arr = id.split(\"&\"); // 存在快捷方式-999的nodeType\n    let callbackData = {\n      actionType: arr[0],\n      colorType: arr[1],\n      objType: data === null || data === void 0 ? void 0 : data.objType,\n      productId: data === null || data === void 0 ? void 0 : data.productId //20250724 Jim Song 后端多加一个参数productId,用来表征如果menuStatus异常时，知晓是哪一个产品id\n    };\n    console.log(callbackData);\n    onContextMenuClick(callbackData);\n  };\n  const _onMoreBtnClick = e => {\n    onMoreBtnClick && onMoreBtnClick({\n      nodeItem: cacheRef.current.nodeData,\n      ctxType: e.actionType,\n      colorType: e.colorType,\n      ...callbackParams\n    });\n  };\n  const _onCreateBtnClick = e => {\n    onCreateBtnClick && onCreateBtnClick({\n      nodeItem: cacheRef.current.nodeData,\n      nodeType: e.colorType,\n      ...callbackParams\n    });\n  };\n  const onContextMenuClick = ({\n    actionType,\n    colorType,\n    productId\n  }) => {\n    if (actionType == eCtxTypeId.ctx_38_create) {\n      // 新建操作\n      // 注意:设置图标颜色无法查询出来\n      const node = findByActionAndColorType(createTypeList, actionType, colorType);\n      if (node.menuStatus == eMenuStatus.status_1_Free_QuotaExceed) {\n        // 免费对象数已达上限\n        return resourceMaxModal(teamId, managerFlag, node.name, node.menuStatus, node.expirationDt, productId);\n      }\n      if (node.menuStatus == eMenuStatus.status_3_Vip_Unauthorized) {\n        // Vip未授权\n        return unVipModal(teamId, managerFlag, node.name, node.menuStatus, node.expirationDt, productId, applyProductAuthorize);\n      }\n      if (node.menuStatus == eMenuStatus.status_4_Vip_Expired) {\n        // Vip已过期\n        return expiredModal(teamId, managerFlag, node.name, node.menuStatus, node.expirationDt, productId);\n      }\n      _onCreateBtnClick({\n        colorType\n      });\n    } else {\n      _onMoreBtnClick({\n        actionType,\n        colorType\n      });\n    }\n  };\n\n  // 根据actionType和colorType查找节点\n  function findByActionAndColorType(list, actionType, colorType) {\n    for (let i in list) {\n      if (list[i].actionType == actionType && list[i].colorType == colorType) {\n        return list[i];\n      }\n      if (list[i].children) {\n        let node = findByActionAndColorType(list[i].children, actionType, colorType);\n        if (node) {\n          return node;\n        }\n      }\n    }\n  }\n\n  // 右击菜单\n  // CtxMenuItem、CtxSubmenu <'fade' | 'scale' | 'flip' | 'slide'>\n  return /*#__PURE__*/_jsxDEV(CtxMenu, {\n    id: id,\n    animation: {\n      enter: false,\n      exit: 'slide'\n    },\n    onVisibilityChange: handleOnVisibilityChange,\n    className: \"\",\n    children: [isCtxLoading && /*#__PURE__*/_jsxDEV(ContextLoadingBoard, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 694,\n      columnNumber: 24\n    }, this), !isCtxLoading && createTypeList.length == 0 && /*#__PURE__*/_jsxDEV(CtxMenuItem, {\n      disabled: true,\n      children: \" \\u65E0\\u53EF\\u7528\\u9009\\u9879 \"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 696,\n      columnNumber: 55\n    }, this), !isCtxLoading && createTypeList.length > 0 && createTypeList.map(el => {\n      const key = `${el.actionType}&${el.colorType}`;\n      if (!isEmpty(el.children)) {\n        return /*#__PURE__*/_jsxDEV(DelayedCtxSubMenu, {\n          label: /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"iconfont \" + getCtxIconByType(el.actionType) + \" fontsize-12 fontcolor-normal marginRight-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: el.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 704,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true)\n          // 子级菜单箭头\n          ,\n          arrow: /*#__PURE__*/_jsxDEV(RightOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 20\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: el.actionType == eCtxTypeId.ctx_38_create ? \"horizontal-submenu-panel\" : \"\",\n            children: el.actionType == eCtxTypeId.ctx_38_create ?\n            // 新建菜单：按分组显示\n            (() => {\n              const items = [];\n              let currentGroupItems = [];\n              el.children.forEach((sub, index) => {\n                const key = `${sub.actionType}&${sub.colorType}`;\n\n                // 如果是分组标题\n                if (sub.type == eCtxOptionType.eGroup) {\n                  // 如果有累积的分组内容，先渲染它们\n                  if (currentGroupItems.length > 0) {\n                    items.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"group-content-row\",\n                      children: currentGroupItems\n                    }, `group-${index}-content`, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 725,\n                      columnNumber: 27\n                    }, this));\n                    currentGroupItems = [];\n                  }\n\n                  // 添加分组标题\n                  items.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"group-title-row\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"group-title\",\n                      children: sub.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 735,\n                      columnNumber: 27\n                    }, this)\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 734,\n                    columnNumber: 25\n                  }, this));\n                } else {\n                  // 如果是分组内容\n                  currentGroupItems.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"submenu-item-wrapper normal-menu-item\",\n                    children: /*#__PURE__*/_jsxDEV(CtxMenuItem, {\n                      id: key,\n                      data: sub,\n                      disabled: sub.disabled,\n                      onClick: onClick,\n                      children: [sub.actionType == eCtxTypeId.ctx_38_create ? /*#__PURE__*/_jsxDEV(AppNodeResourceIcon, {\n                        nodeType: sub.colorType,\n                        className: \"fontsize-12 fontcolor-normal marginRight-5\",\n                        style: {\n                          lineHeight: \"24px\"\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 750,\n                        columnNumber: 31\n                      }, this) : sub.actionType == eCtxTypeId.ctx_12_create_shortcut ? getCtxIconByType(sub.colorType) && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"iconfont \" + getCtxIconByType(sub.colorType) + \" fontsize-12 fontcolor-normal marginRight-5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 752,\n                        columnNumber: 67\n                      }, this) : /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 753,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: \"100%\"\n                        },\n                        children: sub.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 755,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `iconfont ${getMenuStatusIcon(sub.menuStatus).icon} fontsize-14 `,\n                        style: {\n                          color: getMenuStatusIcon(sub.menuStatus).iconColor\n                        },\n                        title: sub.menuStatus == eMenuStatus.status_1_Free_QuotaExceed ? '应用免费额度已用完' : sub.menuStatus == eMenuStatus.status_3_Vip_Unauthorized ? '应用未对您授权' : sub.menuStatus == eMenuStatus.status_4_Vip_Expired ? '应用已过期' : ''\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 758,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 766,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 742,\n                      columnNumber: 27\n                    }, this)\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 741,\n                    columnNumber: 25\n                  }, this));\n                }\n              });\n\n              // 处理最后的分组内容\n              if (currentGroupItems.length > 0) {\n                items.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"group-content-row\",\n                  children: currentGroupItems\n                }, \"group-last-content\", false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 776,\n                  columnNumber: 23\n                }, this));\n              }\n              return items;\n            })() :\n            // 其他菜单：保持原有布局\n            el.children.map((sub, index) => {\n              const key = `${sub.actionType}&${sub.colorType}`;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\",\n                children: /*#__PURE__*/_jsxDEV(CtxMenuItem, {\n                  id: key,\n                  data: sub,\n                  disabled: sub.disabled,\n                  onClick: sub.actionType == eCtxTypeId.ctx_18_set_figure_tag || sub.actionType == eCtxTypeId.ctx_3_color_txt || sub.actionType == eCtxTypeId.ctx_42_set_icon || sub.actionType == eCtxTypeId.ctx_46_text_font || sub.actionType == eCtxTypeId.ctx_61_flag_color || sub.actionType == eCtxTypeId.ctx_62_flag_img ? () => {} : onClick,\n                  style: sub.type != eCtxOptionType.eGroup ? {\n                    paddingLeft: \"24px\"\n                  } : {},\n                  className: sub.actionType == eCtxTypeId.ctx_18_set_figure_tag || sub.actionType == eCtxTypeId.ctx_3_color_txt || sub.actionType == eCtxTypeId.ctx_42_set_icon || sub.actionType == eCtxTypeId.ctx_46_text_font || sub.actionType == eCtxTypeId.ctx_61_flag_color || sub.actionType == eCtxTypeId.ctx_62_flag_img ? \"context-item-not-focus\" : \"\",\n                  children: [sub.actionType == eCtxTypeId.ctx_38_create ? /*#__PURE__*/_jsxDEV(AppNodeResourceIcon, {\n                    nodeType: sub.colorType,\n                    className: \"fontsize-12 fontcolor-normal marginRight-5\",\n                    style: {\n                      lineHeight: \"24px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 814,\n                    columnNumber: 27\n                  }, this) : sub.actionType == eCtxTypeId.ctx_12_create_shortcut ? getCtxIconByType(sub.colorType) && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"iconfont \" + getCtxIconByType(sub.colorType) + \" fontsize-12 fontcolor-normal marginRight-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 816,\n                    columnNumber: 63\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 817,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: \"100%\"\n                    },\n                    children: sub.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 819,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `iconfont ${getMenuStatusIcon(sub.menuStatus).icon} fontsize-14 `,\n                    style: {\n                      color: getMenuStatusIcon(sub.menuStatus).iconColor\n                    },\n                    title: sub.menuStatus == eMenuStatus.status_1_Free_QuotaExceed ? '应用免费额度已用完' : sub.menuStatus == eMenuStatus.status_3_Vip_Unauthorized ? '应用未对您授权' : sub.menuStatus == eMenuStatus.status_4_Vip_Expired ? '应用已过期' : ''\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 822,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 830,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 790,\n                  columnNumber: 23\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 13\n          }, this)\n        }, key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 701,\n          columnNumber: 18\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(CtxMenuItem, {\n          id: key,\n          onClick: onClick,\n          children: [el.actionType == 28 ? /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"iconfont \" + getCtxIconByType(el.actionType) + ' fontsize-12 fontcolor-normal',\n            style: {\n              marginLeft: -5,\n              marginRight: 5\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 841,\n            columnNumber: 13\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"iconfont \" + getCtxIconByType(el.actionType) + \" fontsize-12 fontcolor-normal marginRight-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 843,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: \"100%\"\n            },\n            children: el.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 845,\n            columnNumber: 13\n          }, this)]\n        }, key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 839,\n          columnNumber: 18\n        }, this);\n      }\n    })]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 693,\n    columnNumber: 5\n  }, this);\n}\n_s2(ContextBoard, \"XucGnAf5rosBS/yyawAlko21CMQ=\", false, function () {\n  return [useQuerySetting327_getTeamSpaceAadmin, useQuerySetting407_getCodeValueList, useMutation, useMutation, useContextMenu];\n});\n_c6 = ContextBoard;\nfunction ContextLoadingBoard() {\n  return /*#__PURE__*/_jsxDEV(React.Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(CtxMenuItem, {\n      disabled: true,\n      children: /*#__PURE__*/_jsxDEV(Skeleton.Input, {\n        active: true,\n        style: {\n          height: \"24px\",\n          overflow: \"hidden\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 858,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 857,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(CtxMenuItem, {\n      disabled: true,\n      children: /*#__PURE__*/_jsxDEV(Skeleton.Input, {\n        active: true,\n        style: {\n          height: \"24px\",\n          overflow: \"hidden\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 861,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 860,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(CtxMenuItem, {\n      disabled: true,\n      children: /*#__PURE__*/_jsxDEV(Skeleton.Input, {\n        active: true,\n        style: {\n          height: \"24px\",\n          overflow: \"hidden\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 864,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 863,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(CtxMenuItem, {\n      disabled: true,\n      children: /*#__PURE__*/_jsxDEV(Skeleton.Input, {\n        active: true,\n        style: {\n          height: \"24px\",\n          overflow: \"hidden\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 867,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 866,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(CtxMenuItem, {\n      disabled: true,\n      children: /*#__PURE__*/_jsxDEV(Skeleton.Input, {\n        active: true,\n        style: {\n          height: \"24px\",\n          overflow: \"hidden\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 870,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 869,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 856,\n    columnNumber: 10\n  }, this);\n}\n_c7 = ContextLoadingBoard;\nexport default _c8 = /*#__PURE__*/forwardRef(ContextBoard);\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"ColorSelectedMenu\");\n$RefreshReg$(_c2, \"TitleSelectedMenu\");\n$RefreshReg$(_c3, \"FontSelectedMenu\");\n$RefreshReg$(_c4, \"IconSelectedMenu\");\n$RefreshReg$(_c5, \"DelayedCtxSubMenu\");\n$RefreshReg$(_c6, \"ContextBoard\");\n$RefreshReg$(_c7, \"ContextLoadingBoard\");\n$RefreshReg$(_c8, \"%default%\");", "map": {"version": 3, "names": ["CheckOutlined", "RightOutlined", "team_036_get_node_ctx_options", "AppNodeResourceIcon", "useQuerySetting327_getTeamSpaceAadmin", "useQuerySetting407_getCodeValueList", "isEmpty", "treeToArray", "getSysIconList", "assembleGroup", "eCtxOptionType", "eEnableFlg", "eProductId", "eCtxTypeId", "eMenuStatus", "getCtxIconByType", "getMenuStatusIcon", "eNameTextFontType", "expiredModal", "resourceMaxModal", "unVipModal", "Checkbox", "Space", "Skeleton", "Modal", "React", "forwardRef", "useImperativeHandle", "useRef", "useState", "useEffect", "<PERSON><PERSON>", "CtxMenuItem", "<PERSON><PERSON>", "CtxMenu", "Submenu", "CtxSubMenu", "useContextMenu", "Separator", "formatSvg", "useMutation", "https", "setting_320_get_node_priv_query", "globalUtil", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ColorSelectedMenu", "selectValue", "onChange", "colorOptionsList", "actionType", "_onChanged", "checkedValue", "value", "length", "id", "style", "display", "children", "Group", "map", "item", "name", "type", "toString", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "TitleSelectedMenu", "title", "_c2", "FontSelectedMenu", "optionList", "size", "option", "onClick", "icon", "_c3", "IconSelectedMenu", "_c4", "getCheckItem", "flag", "label", "color", "justifyContent", "alignItems", "DelayedCtxSubMenu", "arrow", "props", "_s", "timeoutRef", "isVisible", "setIsVisible", "menuRef", "subMenuRef", "isMouseInMenu", "subMenuPosition", "setSubMenuPosition", "top", "left", "subMenuHeight", "setSubMenuHeight", "calculateEstimatedHeight", "menuItems", "Children", "toArray", "itemCount", "countItems", "items", "for<PERSON>ach", "isValidElement", "Array", "isArray", "ITEM_HEIGHT", "MENU_PADDING", "Math", "min", "window", "innerHeight", "current", "resizeObserver", "ResizeObserver", "entries", "entry", "contentRect", "height", "observe", "disconnect", "showSubMenu", "clearTimeout", "setTimeout", "menuRect", "getBoundingClientRect", "subMenuRect", "windowHeight", "windowWidth", "innerWidth", "actualHeight", "subMenuWidth", "width", "topPosition", "leftPosition", "bottomOverflow", "max", "rightOverflow", "right", "classList", "add", "remove", "hideSubMenu", "isValidNode", "node", "Node", "e", "handleMouseEnter", "handleMouseLeave", "_menuRef$current", "relatedTarget", "contains", "parentMenu", "parentElement", "handleSubMenuMouseEnter", "handleSubMenuMouseLeave", "_menuRef$current2", "ref", "onMouseEnter", "onMouseLeave", "position", "zIndex", "maxHeight", "overflowY", "_c5", "ContextBoard", "teamId", "onMoreBtnClick", "onCreateBtnClick", "handleOnVisibilityChange", "callbackP<PERSON>ms", "_s2", "data", "userId", "teamAdminFlag", "manager<PERSON><PERSON>", "undefined", "enabled", "selectionList", "isLoading", "isCtxLoading", "createTypeList", "mutateAsync", "mutationFn", "nodeData", "nodeId", "filterActionTypes", "ctxOptionList", "nodeType", "loadContextMenuList", "setting334Mutation", "setting_334_apply_authorization", "applyProductAuthorize", "productId", "mutate", "onSuccess", "result", "resultCode", "info", "content", "maskClosable", "okText", "cacheRef", "show", "showContextBoard", "_onShow", "menuHeight", "menuWidth", "clickY", "clientY", "clickX", "clientX", "adjustedY", "adjustedX", "event", "x", "y", "favoriteFlg", "filter", "ctxOption", "some", "ctx_38_create", "assembleCreateTypeList", "ctx_39_personalization", "assemblePersonalizationList", "ctx_60_flag_mail", "flagMailList", "ctx_37_favorite", "favoriteFlag", "enable", "colorType", "disable", "ctx_40_top", "topFlag", "topFlg", "ctx_41_read_op", "readFlag", "readFlg", "_sub", "_createTypeList", "_createType", "personalizationList", "_personalizationList", "_personalization", "ctx_18_set_figure_tag", "_nodeData$rightFlgIco", "iconColorOptions", "_child", "JSON", "parse", "error", "console", "rightFlgIconType", "ctx_3_color_txt", "_nodeData$nameTextCol", "textColorOptions", "log", "nameTextColorType", "ctx_46_text_font", "_nodeData$nodeIconTyp", "textFontTypeOptions", "_sysIconList$find", "_nodeData$nameTextFon", "sysIconList", "find", "sys", "propType", "propValue", "nameTextFontTypeList", "nameTextFontType", "split", "idx", "nodeIconType", "ctx_42_set_icon", "_nodeData$nodeIconTyp3", "titleIconOptions", "_sysIconList$find2", "_nodeData$nodeIconTyp2", "nodeIcon", "ctx_61_flag_color", "_nodeData$tagColor", "tagColor", "ctx_62_flag_img", "_nodeData$tagColor3", "_getSysIconList$find", "_nodeData$tagColor2", "flatMap", "taskGroup", "triggerEvent", "args", "arr", "callbackData", "objType", "onContextMenuClick", "_onMoreBtnClick", "nodeItem", "ctxType", "_onCreateBtnClick", "findByActionAndColorType", "menuStatus", "status_1_Free_QuotaExceed", "expirationDt", "status_3_Vip_Unauthorized", "status_4_Vip_Expired", "list", "i", "animation", "enter", "exit", "onVisibilityChange", "ContextLoadingBoard", "disabled", "el", "key", "currentGroupItems", "sub", "index", "eGroup", "push", "lineHeight", "ctx_12_create_shortcut", "iconColor", "paddingLeft", "marginLeft", "marginRight", "_c6", "Input", "active", "overflow", "_c7", "_c8", "$RefreshReg$"], "sources": ["F:/Ficent/work/tms_git/tms_dhx_tms_frontend/tms-task/src/common/components/ContextBoard.jsx"], "sourcesContent": ["import { CheckOutlined, RightOutlined } from '@ant-design/icons';\r\nimport { team_036_get_node_ctx_options } from \"@common/api/http\";\r\nimport AppNodeResourceIcon from \"@components/AppNodeResourceIcon\";\r\nimport { useQuerySetting327_getTeamSpaceAadmin, useQuerySetting407_getCodeValueList } from \"@common/service/commonHooks\";\r\nimport { isEmpty, treeToArray, getSysIconList, assembleGroup } from \"@common/utils/ArrayUtils\";\r\nimport { eCtxOptionType, eEnableFlg, eProductId } from \"@common/utils/enum\";\r\nimport { eCtxTypeId, eMenuStatus, getCtxIconByType, getMenuStatusIcon, eNameTextFontType } from \"@common/utils/TsbConfig\";\r\nimport { expiredModal, resourceMaxModal, unVipModal } from \"@common/utils/ViewUtils\";\r\nimport { Checkbox, Space, Skeleton, Modal } from \"antd\";\r\nimport React, { forwardRef, useImperativeHandle, useRef, useState, useEffect } from \"react\";\r\nimport { Item as CtxMenuItem, Menu as CtxMenu, Submenu as CtxSubMenu, useContextMenu, Separator } from \"react-contexify\";\r\nimport { formatSvg } from \"@common/utils/ViewUtils\";\r\nimport \"react-contexify/ReactContexify.css\";\r\nimport { useMutation } from '@tanstack/react-query';\r\nimport \"./ContextBoard.scss\";\r\nimport * as https from \"@common/api/http\";\r\nimport { setting_320_get_node_priv_query } from \"@common/api/query/query_setting\";\r\nimport { globalUtil } from \"@common/utils/globalUtil\";\r\n\r\n\r\n// 图标颜色\r\nfunction ColorSelectedMenu({ selectValue, onChange, colorOptionsList, actionType }) {\r\n  const _onChanged = (checkedValue) => {\r\n    let value = checkedValue.length ? checkedValue[checkedValue.length - 1] : \"\" // 选中/取消选中 多选，由数组存储，选中后，前一个选中不会自动取消，因此要取最后一个最新选中的值\r\n    onChange && onChange({ id: actionType + \"&\" + value })\r\n  }\r\n  return <div style={{ display: \"inline-block\" }}>\r\n    <Checkbox.Group value={[selectValue]} onChange={_onChanged}>\r\n      {colorOptionsList.map(item => <Checkbox name=\"xxx\" key={item.type.toString()} value={item.type.toString()} className={item.className} ></Checkbox>)}\r\n    </Checkbox.Group>\r\n  </div>\r\n}\r\n\r\n// 标题颜色\r\nfunction TitleSelectedMenu({ selectValue, onChange, colorOptionsList, actionType }) {\r\n  const _onChanged = (checkedValue) => {\r\n    let value = checkedValue.length ? checkedValue[checkedValue.length - 1] : \"\" // 选中/取消选中 多选，由数组存储，选中后，前一个选中不会自动取消，因此要取最后一个最新选中的值\r\n    onChange && onChange({ id: actionType + \"&\" + value })\r\n  }\r\n  return <div style={{ display: \"inline-block\" }}>\r\n    <Checkbox.Group value={[selectValue]} onChange={_onChanged} >\r\n      {colorOptionsList.map(item => <Checkbox name=\"xxx\" key={item.type.toString()} value={item.type.toString()} className={item.className} >\r\n      { selectValue == item.type ? \"\" : item.title}\r\n      </Checkbox>)}\r\n    </Checkbox.Group>\r\n  </div>\r\n}\r\n\r\n// 字体个性化设置，前段需要固定识别，存储的是type值\r\nfunction FontSelectedMenu({ onChange, optionList, actionType }) {\r\n  const _onChanged = (checkedValue) => {\r\n    onChange && onChange({ id: actionType + \"&\" + checkedValue })\r\n  }\r\n  return <Space size={10}>\r\n    {optionList.map((option)=>(\r\n      <div key={option?.type} onClick={()=>_onChanged(option?.type)} className={option.className}>\r\n       {formatSvg(option.icon)}\r\n      </div>\r\n    ))}\r\n  </Space>\r\n}\r\n\r\n// 图标设置，无需固定，有多少显示多少，存储的是value值\r\nfunction IconSelectedMenu({ onChange, optionList, actionType }) {\r\n  const _onChanged = (checkedValue) => {\r\n    onChange && onChange({ id: actionType + \"&\" + checkedValue })\r\n  }\r\n  return <Space size={10}>\r\n    {optionList.map((option)=>(\r\n      <div key={option?.type} onClick={()=>_onChanged(option?.value)} className={option.className}>\r\n       {formatSvg(option.icon)}\r\n      </div>\r\n    ))}\r\n  </Space>\r\n}\r\n\r\n/**\r\n * @description 文档图标\r\n */\r\n/*function TitleIconSelectedMenu({ selectValue, onChange, colorOptionsList }) {\r\n  const _onChanged = (checkedValue) => {\r\n    let value = checkedValue.length ? checkedValue[checkedValue.length - 1] : \"\" // 选中/取消选中 多选，由数组存储，选中后，前一个选中不会自动取消，因此要取最后一个最新选中的值\r\n    onChange && onChange({ id: eCtxTypeId.ctx_42_set_icon + \"&\" + value })\r\n  }\r\n  return <div style={{ display: \"inline-block\" }}>\r\n  <Checkbox.Group value={[selectValue]} onChange={_onChanged}>\r\n    {colorOptionsList.map(option => (\r\n      <Checkbox\r\n        key={option.value?.toString()} \r\n        value={option.value?.toString()}\r\n        className=\"checkbox-bage\"\r\n      >\r\n        <div className=\"checkbox-bage-icon\">\r\n          {formatSvg(option.icon)}\r\n        </div>\r\n      </Checkbox>\r\n    ))}\r\n  </Checkbox.Group>\r\n</div>\r\n}*/\r\n\r\n/*// checkbox 选中状态\r\nconst getCheckboxItem = (flag, label, color, className, actionType) => {\r\n  return <div style={{ display: \"flex\", justifyContent: \"space-between\", alignItems: \"baseline\" }}>\r\n    {/!* tmsbug-2622：删除线文案本身添加删除线 *!/}\r\n    <span style={{ color: color }} className={`${actionType == eCtxTypeId.ctx_46_text_font ? \"tree-dir-title-delete\" : \"\"}`}>{label}</span>\r\n    <Checkbox checked={flag} className={className} />\r\n  </div>\r\n}*/\r\n\r\n// 收藏选中状态\r\nconst getCheckItem = (flag, label, color) => {\r\n  return <div style={{ display: \"flex\", justifyContent: \"space-between\", alignItems: \"center\", color }}>\r\n    <span>{label}</span>\r\n    {flag ? <CheckOutlined style={{ color: \"#70b603\" }} /> : <></>}\r\n  </div>\r\n}\r\n\r\n// 自定义延迟子菜单组件\r\nconst DelayedCtxSubMenu = ({ children, label, arrow, ...props }) => {\r\n  const timeoutRef = useRef(null);\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const menuRef = useRef(null);\r\n  const subMenuRef = useRef(null);\r\n  const isMouseInMenu = useRef(false);\r\n  const [subMenuPosition, setSubMenuPosition] = useState({ top: 0, left: '100%' });\r\n  const [subMenuHeight, setSubMenuHeight] = useState(0);\r\n\r\n  // 计算预估的子菜单高度\r\n  const calculateEstimatedHeight = () => {\r\n    // 获取子菜单项的数量\r\n    const menuItems = React.Children.toArray(children);\r\n    let itemCount = 0;\r\n\r\n    // 递归计算所有菜单项（包括分组内的项）\r\n    const countItems = (items) => {\r\n      items.forEach(item => {\r\n        if (React.isValidElement(item)) {\r\n          if (item.props && item.props.children) {\r\n            if (Array.isArray(item.props.children)) {\r\n              countItems(item.props.children);\r\n            } else {\r\n              itemCount++;\r\n            }\r\n          } else {\r\n            itemCount++;\r\n          }\r\n        }\r\n      });\r\n    };\r\n\r\n    countItems(menuItems);\r\n\r\n    // 单个菜单项的高度（包括padding和border）\r\n    const ITEM_HEIGHT = 32; // 增加高度估算，考虑到实际渲染可能更高\r\n    // 子菜单的padding和边距\r\n    const MENU_PADDING = 16; // 增加padding估算\r\n\r\n    return Math.min(itemCount * ITEM_HEIGHT + MENU_PADDING, window.innerHeight - 40); // 限制最大高度\r\n  };\r\n\r\n  // 添加 ResizeObserver 监听子菜单高度变化\r\n  useEffect(() => {\r\n    if (!subMenuRef.current) return;\r\n\r\n    const resizeObserver = new ResizeObserver(entries => {\r\n      for (let entry of entries) {\r\n        setSubMenuHeight(entry.contentRect.height);\r\n      }\r\n    });\r\n\r\n    resizeObserver.observe(subMenuRef.current);\r\n\r\n    return () => {\r\n      resizeObserver.disconnect();\r\n    };\r\n  }, [isVisible]);\r\n\r\n  const showSubMenu = () => {\r\n    isMouseInMenu.current = true;\r\n    if (timeoutRef.current) {\r\n      clearTimeout(timeoutRef.current);\r\n    }\r\n\r\n    setIsVisible(true);\r\n\r\n    // 延迟计算位置，确保子菜单已经渲染到DOM中\r\n    setTimeout(() => {\r\n      if (menuRef.current && subMenuRef.current) {\r\n        const menuRect = menuRef.current.getBoundingClientRect();\r\n        const subMenuRect = subMenuRef.current.getBoundingClientRect();\r\n        const windowHeight = window.innerHeight;\r\n        const windowWidth = window.innerWidth;\r\n\r\n        // 获取实际的子菜单高度，如果还没有渲染完成则使用估算高度\r\n        const actualHeight = subMenuRect.height > 0 ? subMenuRect.height : calculateEstimatedHeight();\r\n        const subMenuWidth = subMenuRect.width > 0 ? subMenuRect.width : 200; // 默认宽度\r\n\r\n        // 默认与父菜单项顶部对齐，并往上偏移50px\r\n        let topPosition = -50;\r\n        let leftPosition = '100%';\r\n\r\n        // 检查子菜单底部是否会超出屏幕\r\n        const bottomOverflow = menuRect.top + actualHeight - windowHeight;\r\n        if (bottomOverflow > 0) {\r\n          // 如果会超出屏幕底部，向上调整位置\r\n          topPosition = Math.max(-menuRect.top + 10, -bottomOverflow - 60); // 至少留10px的边距\r\n        }\r\n\r\n        // 检查子菜单右侧是否会超出屏幕\r\n        const rightOverflow = menuRect.right + subMenuWidth - windowWidth;\r\n        if (rightOverflow > 0) {\r\n          // 如果会超出屏幕右侧，显示在左侧\r\n          leftPosition = `-${subMenuWidth}px`;\r\n          // 添加CSS类标识左侧定位\r\n          if (subMenuRef.current) {\r\n            subMenuRef.current.classList.add('submenu-left-positioned');\r\n          }\r\n        } else {\r\n          // 移除左侧定位类\r\n          if (subMenuRef.current) {\r\n            subMenuRef.current.classList.remove('submenu-left-positioned');\r\n          }\r\n        }\r\n\r\n        setSubMenuPosition({ top: topPosition, left: leftPosition });\r\n      }\r\n    }, 10); // 给一个小的延迟确保DOM更新\r\n  };\r\n\r\n  const hideSubMenu = () => {\r\n    if (timeoutRef.current) {\r\n      clearTimeout(timeoutRef.current);\r\n    }\r\n    timeoutRef.current = setTimeout(() => {\r\n      if (!isMouseInMenu.current) {\r\n        setIsVisible(false);\r\n      }\r\n    }, 1000);\r\n  };\r\n\r\n  // 检查是否是有效的DOM节点，解决截图时焦点失去报错问题；\r\n  const isValidNode = (node) => {\r\n    try {\r\n      return node && (node instanceof Node);\r\n    } catch (e) {\r\n      return false;\r\n    }\r\n  };\r\n\r\n  const handleMouseEnter = () => {\r\n    showSubMenu();\r\n  };\r\n\r\n  const handleMouseLeave = (e) => {\r\n    \r\n    // 检查鼠标是否移动到子菜单\r\n    if (subMenuRef.current && isValidNode(e.relatedTarget) && subMenuRef.current.contains(e.relatedTarget)) {\r\n      return;\r\n    }\r\n    \r\n    // 检查鼠标是否移动到其他父菜单\r\n    const parentMenu = menuRef.current?.parentElement;\r\n    if (parentMenu && isValidNode(e.relatedTarget) && parentMenu.contains(e.relatedTarget)) {\r\n      return;\r\n    }\r\n\r\n    isMouseInMenu.current = false;\r\n    hideSubMenu();\r\n  };\r\n\r\n  const handleSubMenuMouseEnter = () => {\r\n    showSubMenu();\r\n  };\r\n\r\n  const handleSubMenuMouseLeave = (e) => {\r\n    \r\n    // 检查鼠标是否移动到主菜单\r\n    if (menuRef.current && isValidNode(e.relatedTarget) && menuRef.current.contains(e.relatedTarget)) {\r\n      return;\r\n    }\r\n\r\n    // 检查鼠标是否移动到其他父菜单\r\n    const parentMenu = menuRef.current?.parentElement;\r\n    if (parentMenu && isValidNode(e.relatedTarget) && parentMenu.contains(e.relatedTarget)) {\r\n      return;\r\n    }\r\n\r\n    isMouseInMenu.current = false;\r\n    hideSubMenu();\r\n  };\r\n\r\n  React.useEffect(() => {\r\n    return () => {\r\n      if (timeoutRef.current) {\r\n        clearTimeout(timeoutRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <div\r\n      ref={menuRef}\r\n      onMouseEnter={handleMouseEnter}\r\n      onMouseLeave={handleMouseLeave}\r\n      className=\"delayed-submenu-container\"\r\n      style={{ position: 'relative' }}\r\n    >\r\n      <CtxSubMenu\r\n        {...props}\r\n        label={label}\r\n        arrow={arrow}\r\n        style={{\r\n          ...props.style,\r\n          position: 'absolute',\r\n          left: subMenuPosition.left,\r\n          top: subMenuPosition.top,\r\n          zIndex: 999,\r\n          display: isVisible ? 'block' : 'none',\r\n          maxHeight: 'calc(100vh - 20px)', // 限制最大高度\r\n          overflowY: 'auto' // 如果内容过多，允许滚动\r\n        }}\r\n      >\r\n        <div \r\n          ref={subMenuRef}\r\n          onMouseEnter={handleSubMenuMouseEnter}\r\n          onMouseLeave={handleSubMenuMouseLeave}\r\n          style={{ \r\n            width: '100%', \r\n            height: '100%',\r\n            position: 'relative',\r\n            zIndex: 1000\r\n          }}\r\n        >\r\n          {children}\r\n        </div>\r\n      </CtxSubMenu>\r\n    </div>\r\n  );\r\n};\r\n\r\n/**\r\n * 右击菜单 \r\n * @param teamId 团队Id\r\n * @param onMoreBtnClick 非新建操作回调\r\n * @param onCreateBtnClick 新建等操作回调\r\n * @param id 菜单id\r\n * @param callbackParams 是 onMoreBtnClick 和 onCreateBtnClick 回调返回参数\r\n */\r\nfunction ContextBoard({ teamId, onMoreBtnClick, onCreateBtnClick, id, handleOnVisibilityChange, ...callbackParams }, ref) {\r\n  // const [createTypeList, setCreateTypeList] = useState([]); // tree right click context menu\r\n  // const [nodeData,setNodeData] = useState(null); \r\n  const { data: { userId, teamAdminFlag: managerFlag } = {userId: undefined, teamAdminFlag: undefined} }\r\n    = useQuerySetting327_getTeamSpaceAadmin({teamId, enabled: true}); // 判断登录人员是否是团队管理员\r\n  const { data: selectionList } = useQuerySetting407_getCodeValueList(teamId); //字典数据\r\n\r\n  const {isLoading:isCtxLoading, data:createTypeList=[], mutateAsync} = useMutation({\r\n    mutationFn: ({nodeData,nodeId,filterActionTypes,ctxOptionList, nodeType}) => loadContextMenuList(nodeData,nodeId,filterActionTypes,ctxOptionList, nodeType)\r\n  })\r\n\r\n  const setting334Mutation = useMutation({\r\n    mutationFn: https.setting_334_apply_authorization\r\n  })\r\n\r\n  //产品申请开通授权\r\n  const applyProductAuthorize = (productId) => {\r\n    if(!!productId) {\r\n      setting334Mutation.mutate({ teamId, productId }, {\r\n        onSuccess: (result) => {\r\n          if(result.resultCode === 200) {\r\n            //globalUtil.success(\"提交申请成功！\");\r\n            Modal.info({\r\n              title: \"提示\",\r\n              content: \"提交成功，管理员会接收到申请信息，请您耐心等候\",\r\n              maskClosable: true,\r\n              //centered: true, // 居中\r\n              okText: \"我知道了\",\r\n              width: 500,\r\n            });\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  const cacheRef = useRef({});\r\n  const { show } = useContextMenu({ id: id });\r\n\r\n  useImperativeHandle(ref, () => ({\r\n    /// const nodeData = {\r\n    ///   nodeId,\r\n    ///   rightFlgIconType： 图标颜色\r\n    ///   nameTextColorType：名称颜色\r\n    ///   nameTextStrikeFlg：删除线\r\n    /// }\r\n    showContextBoard: (e, nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType) => _onShow(e, nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType)\r\n  }));\r\n\r\n  // load current node menu context and show\r\n  // ctxOptionList：自定义传入ctxOptionList，无需接口获取\r\n  const _onShow = async (e, nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType) => {\r\n    // 计算菜单位置\r\n    const menuHeight = 300; // 预估菜单高度\r\n    const menuWidth = 200; // 预估菜单宽度\r\n    const windowHeight = window.innerHeight;\r\n    const windowWidth = window.innerWidth;\r\n    const clickY = e.clientY;\r\n    const clickX = e.clientX;\r\n    \r\n    // 如果点击位置靠近底部，向上偏移\r\n    const adjustedY = clickY + menuHeight > windowHeight ? \r\n      Math.max(0, windowHeight - menuHeight - 10) : \r\n      clickY;\r\n\r\n    // 如果点击位置靠近右侧，向左偏移\r\n    const adjustedX = clickX + menuWidth > windowWidth ?\r\n      Math.max(0, windowWidth - menuWidth - 10) :\r\n      clickX;\r\n\r\n    // 使用 react-contexify 的默认定位机制，但添加位置调整\r\n    show({ \r\n      event: e, \r\n      props: nodeData,\r\n      position: {\r\n        x: adjustedX,\r\n        y: adjustedY\r\n      }\r\n    });\r\n    \r\n    mutateAsync({nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType});\r\n    cacheRef.current.nodeData = nodeData;\r\n  }\r\n\r\n  // 特殊逻辑:1.由于报告模块同一份报告针对不同的人是不同类型的信件，根据nodeId无法区分出是发件箱,收件箱还是垃圾箱，所以需要前端传参nodeType来区分右击菜单\r\n  // tmsbug-8830: 新建接口定义的菜单项位置调整，已经如果已匹配过，则不需要呈现\r\n  const loadContextMenuList = async (nodeData, nodeId, filterActionTypes, ctxOptionList, nodeType) => {\r\n    let result = !isEmpty(ctxOptionList) ? { resultCode: 200, ctxOptionList } : await team_036_get_node_ctx_options({ teamId, nodeId, nodeType})\r\n    if (result.resultCode == 200) {\r\n      let {ctxOptionList, favoriteFlg} = result;\r\n      if(!isEmpty(filterActionTypes)) ctxOptionList = ctxOptionList.filter(ctxOption => !(filterActionTypes || []).some(actionType => ctxOption.actionType == actionType)) // 无需新建\r\n      ctxOptionList.forEach(ctxOption => {\r\n        switch (+ ctxOption.actionType) {\r\n          case eCtxTypeId.ctx_38_create: // 新建操作\r\n            ctxOption.children = assembleCreateTypeList(ctxOption.children);\r\n            break;\r\n          case eCtxTypeId.ctx_39_personalization: // 个性化设置\r\n            ctxOption.children = assemblePersonalizationList(ctxOption.children, nodeData);\r\n            break;\r\n          case eCtxTypeId.ctx_60_flag_mail: // 标记邮件\r\n            ctxOption.children = flagMailList(ctxOption.children, nodeData);\r\n            break;\r\n          case eCtxTypeId.ctx_37_favorite: // 收藏\r\n            const favoriteFlag = favoriteFlg == eEnableFlg.enable;\r\n            ctxOption.colorType = favoriteFlag ? eEnableFlg.disable : eEnableFlg.enable;\r\n            ctxOption.name = getCheckItem(favoriteFlag, ctxOption.name);\r\n            break;\r\n          case eCtxTypeId.ctx_40_top: // 置顶（报告独有）\r\n            const topFlag = nodeData.topFlg == eEnableFlg.enable;\r\n            ctxOption.colorType = topFlag ? eEnableFlg.disable : eEnableFlg.enable;\r\n            ctxOption.name = getCheckItem(topFlag, ctxOption.name);\r\n            break;\r\n          case eCtxTypeId.ctx_41_read_op: // 已读/未读（报告独有）\r\n            const readFlag = nodeData.readFlg == eEnableFlg.enable;\r\n            ctxOption.colorType = readFlag ? eEnableFlg.disable : eEnableFlg.enable;\r\n            ctxOption.name = getCheckItem(readFlag, ctxOption.name);\r\n            break;\r\n          default:\r\n            ctxOption.children = ctxOption.children.map((_sub)=>({..._sub, actionType: ctxOption.actionType, colorType: _sub.actionType}));\r\n            break;\r\n        }\r\n      });\r\n      return ctxOptionList;\r\n    } else {\r\n      return [];\r\n    }\r\n  }\r\n\r\n\r\n\r\n  // 给新建菜单增加分组\r\n  const assembleCreateTypeList = (createTypeList) => {\r\n    let _createTypeList = assembleGroup(createTypeList, false);\r\n    _createTypeList = treeToArray(_createTypeList);\r\n    _createTypeList.forEach((_createType)=>{\r\n      _createType.colorType = _createType.actionType;\r\n      _createType.actionType = eCtxTypeId.ctx_38_create;\r\n    });\r\n    return _createTypeList;     \r\n  }\r\n\r\n  // 个性化设置\r\n  const assemblePersonalizationList = (personalizationList, nodeData) => {\r\n    let _personalizationList = assembleGroup(personalizationList, true);\r\n    _personalizationList = _personalizationList.map( _personalization =>{\r\n      if(_personalization.actionType == eCtxTypeId.ctx_18_set_figure_tag){ // 图标颜色\r\n        const iconColorOptions = (_personalization.children || []).map(_child => {\r\n          try {\r\n            return JSON.parse(_child.name);\r\n          } catch (error) {\r\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name );\r\n            return {}\r\n          }\r\n        });\r\n        _personalization.children = [\r\n          {\r\n            actionType: eCtxTypeId.ctx_18_set_figure_tag,\r\n            name: <ColorSelectedMenu selectValue={nodeData.rightFlgIconType?.toString()} onChange={onClick} colorOptionsList = {iconColorOptions} actionType={eCtxTypeId.ctx_18_set_figure_tag}/>,\r\n          }\r\n        ] \r\n        return _personalization;\r\n      } else if(_personalization.actionType == eCtxTypeId.ctx_3_color_txt){ // 标题颜色\r\n        const textColorOptions = (_personalization.children || []).map(_child => {\r\n          try {\r\n            let option = JSON.parse(_child.name);\r\n            return option;\r\n          } catch (error) {\r\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name );\r\n            return {}\r\n          }\r\n        });\r\n       /*  _personalization.children = textColorOptions.map((option, index) => {\r\n          let flag = option.type == nodeData.nameTextColorType;\r\n          return {\r\n            actionType: eCtxTypeId.ctx_3_color_txt,\r\n            colorType: flag ? 0 : option.type,\r\n            name: getCheckboxItem(flag, option.title, option.value, option.className, eCtxTypeId.ctx_3_color_txt),\r\n          }\r\n        }); */\r\n        console.log(\"colorOptionsList\", textColorOptions);\r\n        _personalization.children = [\r\n          {\r\n            actionType: eCtxTypeId.ctx_3_color_txt,\r\n            name: <TitleSelectedMenu selectValue={nodeData.nameTextColorType?.toString()} onChange={onClick} colorOptionsList = {textColorOptions} actionType={eCtxTypeId.ctx_3_color_txt}/>,\r\n          }\r\n        ] \r\n        return _personalization;\r\n      } else if(_personalization.actionType == eCtxTypeId.ctx_46_text_font ){ // 设置字体个性化\r\n        let textFontTypeOptions = (_personalization.children || []).map(_child => {\r\n          try {\r\n            let option =  JSON.parse(_child.name);\r\n            let sysIconList = getSysIconList(selectionList); \r\n            option.icon = sysIconList.find(sys => sys.propType == option.value)?.propValue;\r\n            const nameTextFontTypeList = nodeData.nameTextFontType?.split(\",\") || []; // 字体字段\r\n            option.className = nameTextFontTypeList[eNameTextFontType[option.type].idx] == eEnableFlg.enable ? `checked-gray-box` : \"\";\r\n            return option;\r\n          } catch (error) {\r\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name );\r\n            return {}\r\n          }\r\n        });\r\n        console.log(\"textFontTypeOptions\", textFontTypeOptions);\r\n        _personalization.children = [\r\n          {\r\n            actionType: _personalization.actionType,\r\n            name: <FontSelectedMenu selectValue={nodeData.nodeIconType?.toString()} onChange={onClick} optionList={textFontTypeOptions} actionType={_personalization.actionType}/>,\r\n          }\r\n        ] \r\n        return _personalization;\r\n      } else if(_personalization.actionType == eCtxTypeId.ctx_42_set_icon){ // 设置图标\r\n        let titleIconOptions = (_personalization.children || []).map(_child => {\r\n          try {\r\n            let option =  JSON.parse(_child.name);\r\n            let sysIconList = getSysIconList(selectionList); \r\n            option.icon = sysIconList.find(sys => sys.propType == option.value)?.propValue;\r\n            option.className = (nodeData.nodeIconType?.split(\",\") || []).some(nodeIcon => option.value == nodeIcon ) ? `checked-gray-box` : \"\";\r\n            return option;\r\n          } catch (error) {\r\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name );\r\n            return {}\r\n          }\r\n        });\r\n        _personalization.children = [\r\n          {\r\n            actionType: _personalization.actionType,\r\n            name: <IconSelectedMenu selectValue={nodeData.nodeIconType?.toString()} onChange={onClick} optionList={titleIconOptions} actionType={_personalization.actionType}/>,\r\n          }\r\n        ] \r\n        return _personalization;\r\n      } else  {\r\n        return _personalization;\r\n      }\r\n    })\r\n    _personalizationList = treeToArray(_personalizationList);\r\n    return _personalizationList;\r\n  }\r\n\r\n  // 标记邮件\r\n  const flagMailList = (personalizationList, nodeData) => {\r\n    let _personalizationList = assembleGroup(personalizationList, true);\r\n    _personalizationList = _personalizationList.map( _personalization =>{\r\n      if(_personalization.actionType == eCtxTypeId.ctx_61_flag_color){ // 标记颜色\r\n        const iconColorOptions = (_personalization.children || []).map(_child => {\r\n          try {\r\n            return JSON.parse(_child.name);\r\n          } catch (error) {\r\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name );\r\n            return {}\r\n          }\r\n        });\r\n        _personalization.children = [\r\n          {\r\n            actionType: _personalization.actionType,\r\n            name: <ColorSelectedMenu selectValue={nodeData.tagColor?.toString()} onChange={onClick} colorOptionsList = {iconColorOptions} actionType={_personalization.actionType}/>,\r\n          }\r\n        ] \r\n        return _personalization;\r\n      }  else if(_personalization.actionType == eCtxTypeId.ctx_62_flag_img){ // 标记图标\r\n        let titleIconOptions = (_personalization.children || []).map(_child => {\r\n          try {\r\n            let option =  JSON.parse(_child.name);\r\n            option.icon = getSysIconList(selectionList).find(sys => sys.propType == option.value)?.propValue;\r\n            option.className = (nodeData.tagColor?.split(\",\") || []).some(nodeIcon => option.value == nodeIcon ) ? `checked-gray-box` : \"\";\r\n            return option;\r\n          } catch (error) {\r\n            console.error(\"右击菜单个性化设置JSON格式不合法,请检查\", _child.name );\r\n            return {}\r\n          }\r\n        });\r\n        _personalization.children = [\r\n          {\r\n            actionType: _personalization.actionType,\r\n            name: <IconSelectedMenu selectValue={nodeData.tagColor?.toString()} onChange={onClick} optionList={titleIconOptions} actionType={_personalization.actionType}/>,\r\n          }\r\n        ] \r\n        return _personalization;\r\n      } else  {\r\n        return _personalization;\r\n      }\r\n    })\r\n    _personalizationList =  _personalizationList.flatMap((taskGroup) => taskGroup.children);\r\n    return _personalizationList;\r\n  }\r\n\r\n\r\n  // menu item click\r\n  const onClick = ({ id, props, data, triggerEvent, ...args }) => {\r\n    let arr = id.split(\"&\") // 存在快捷方式-999的nodeType\r\n    let callbackData = {\r\n      actionType: arr[0],\r\n      colorType: arr[1],\r\n      objType: data?.objType,\r\n      productId: data?.productId //20250724 Jim Song 后端多加一个参数productId,用来表征如果menuStatus异常时，知晓是哪一个产品id\r\n    }\r\n    console.log(callbackData)\r\n    onContextMenuClick(callbackData)\r\n  }\r\n\r\n  const _onMoreBtnClick = (e) => {\r\n    onMoreBtnClick && onMoreBtnClick({ nodeItem: cacheRef.current.nodeData, ctxType: e.actionType, colorType: e.colorType, ...callbackParams })\r\n  }\r\n\r\n  const _onCreateBtnClick = (e) => {\r\n    onCreateBtnClick && onCreateBtnClick({ nodeItem: cacheRef.current.nodeData, nodeType: e.colorType, ...callbackParams})\r\n  }\r\n\r\n  const onContextMenuClick = ({ actionType, colorType, productId }) => {\r\n    if (actionType == eCtxTypeId.ctx_38_create) { // 新建操作\r\n      // 注意:设置图标颜色无法查询出来\r\n      const node = findByActionAndColorType(createTypeList, actionType, colorType);\r\n      if(node.menuStatus == eMenuStatus.status_1_Free_QuotaExceed ){ // 免费对象数已达上限\r\n        return resourceMaxModal(teamId, managerFlag, node.name, node.menuStatus, node.expirationDt, productId);\r\n      }\r\n      if(node.menuStatus == eMenuStatus.status_3_Vip_Unauthorized ){ // Vip未授权\r\n        return unVipModal(teamId, managerFlag, node.name, node.menuStatus, node.expirationDt, productId,applyProductAuthorize);\r\n      }\r\n      if(node.menuStatus == eMenuStatus.status_4_Vip_Expired ){ // Vip已过期\r\n        return expiredModal(teamId, managerFlag, node.name, node.menuStatus, node.expirationDt, productId);\r\n      }\r\n      _onCreateBtnClick({ colorType })\r\n    } else {\r\n      _onMoreBtnClick({ actionType, colorType })\r\n    }\r\n  }\r\n\r\n  // 根据actionType和colorType查找节点\r\n  function findByActionAndColorType(list, actionType, colorType) {\r\n      for (let i in list) {\r\n        if (list[i].actionType == actionType && list[i].colorType == colorType) {\r\n          return list[i];\r\n        }\r\n        if (list[i].children) {\r\n          let node = findByActionAndColorType(list[i].children, actionType, colorType);\r\n          if (node) {\r\n            return node\r\n          }\r\n        }\r\n      }\r\n  }\r\n\r\n  // 右击菜单\r\n  // CtxMenuItem、CtxSubmenu <'fade' | 'scale' | 'flip' | 'slide'>\r\n  return (\r\n    <CtxMenu id={id} animation={{ enter: false, exit: 'slide' }} onVisibilityChange={handleOnVisibilityChange} className=\"\">\r\n      {isCtxLoading && <ContextLoadingBoard />}\r\n      {/* 无可用选项 */}\r\n      {!isCtxLoading && createTypeList.length == 0 && <CtxMenuItem disabled> 无可用选项 </CtxMenuItem>}\r\n      {/* 逻辑确定只有两层所以这么处理 */}\r\n      {!isCtxLoading && createTypeList.length > 0 && createTypeList.map(el => {\r\n        const key = `${el.actionType}&${el.colorType}`;\r\n        if (!isEmpty(el.children)) {\r\n          return <DelayedCtxSubMenu key={key} label={\r\n            <>\r\n              <span className={\"iconfont \" + getCtxIconByType(el.actionType) + \" fontsize-12 fontcolor-normal marginRight-5\"}></span>\r\n              <span>{el.name}</span>\r\n            </>}\r\n            // 子级菜单箭头\r\n            arrow={<RightOutlined />}\r\n          >\r\n            {/* 横向子菜单面板 - 只对新建菜单使用 */}\r\n            <div className={el.actionType == eCtxTypeId.ctx_38_create ? \"horizontal-submenu-panel\" : \"\"}>\r\n              {el.actionType == eCtxTypeId.ctx_38_create ? (\r\n                // 新建菜单：按分组显示\r\n                (() => {\r\n                  const items = [];\r\n                  let currentGroupItems = [];\r\n                  \r\n                  el.children.forEach((sub, index) => {\r\n                    const key = `${sub.actionType}&${sub.colorType}`;\r\n                    \r\n                    // 如果是分组标题\r\n                    if (sub.type == eCtxOptionType.eGroup) {\r\n                      // 如果有累积的分组内容，先渲染它们\r\n                      if (currentGroupItems.length > 0) {\r\n                        items.push(\r\n                          <div key={`group-${index}-content`} className=\"group-content-row\">\r\n                            {currentGroupItems}\r\n                          </div>\r\n                        );\r\n                        currentGroupItems = [];\r\n                      }\r\n                      \r\n                      // 添加分组标题\r\n                      items.push(\r\n                        <div key={index} className=\"group-title-row\">\r\n                          <div className=\"group-title\">{sub.name}</div>\r\n                        </div>\r\n                      );\r\n                    } else {\r\n                      // 如果是分组内容\r\n                      currentGroupItems.push(\r\n                        <div key={index} className=\"submenu-item-wrapper normal-menu-item\">\r\n                          <CtxMenuItem\r\n                            id={key}\r\n                            data={sub}\r\n                            disabled={sub.disabled}\r\n                            onClick={onClick}\r\n                          >\r\n                            {\r\n                              sub.actionType == eCtxTypeId.ctx_38_create ?\r\n                              <AppNodeResourceIcon nodeType={sub.colorType} className=\"fontsize-12 fontcolor-normal marginRight-5\" style={{lineHeight: \"24px\"}}/>: \r\n                              sub.actionType == eCtxTypeId.ctx_12_create_shortcut ?\r\n                              (getCtxIconByType(sub.colorType) && <span className={\"iconfont \" + getCtxIconByType(sub.colorType) + \" fontsize-12 fontcolor-normal marginRight-5\"}></span>) :\r\n                              <span></span>\r\n                            }\r\n                            <div style={{ width: \"100%\" }}>{sub.name}</div>\r\n                            {/* 对象状态图标 */}\r\n                            {\r\n                              <span className = {`iconfont ${getMenuStatusIcon(sub.menuStatus).icon} fontsize-14 `}\r\n                               style={{color: getMenuStatusIcon(sub.menuStatus).iconColor}}\r\n                               title={sub.menuStatus == eMenuStatus.status_1_Free_QuotaExceed ?\r\n                                   '应用免费额度已用完' : sub.menuStatus == eMenuStatus.status_3_Vip_Unauthorized ?\r\n                                                    '应用未对您授权' : sub.menuStatus == eMenuStatus.status_4_Vip_Expired ?\r\n                                                                   '应用已过期' : ''}>\r\n                              </span>\r\n                            }\r\n                            <span></span>\r\n                          </CtxMenuItem>\r\n                        </div>\r\n                      );\r\n                    }\r\n                  });\r\n                  \r\n                  // 处理最后的分组内容\r\n                  if (currentGroupItems.length > 0) {\r\n                    items.push(\r\n                      <div key=\"group-last-content\" className=\"group-content-row\">\r\n                        {currentGroupItems}\r\n                      </div>\r\n                    );\r\n                  }\r\n                  \r\n                  return items;\r\n                })()\r\n              ) : (\r\n                // 其他菜单：保持原有布局\r\n                el.children.map((sub, index) => {\r\n                  const key = `${sub.actionType}&${sub.colorType}`;\r\n                  return (\r\n                    <div key={index} className=\"\">\r\n                      <CtxMenuItem\r\n                        id={key}\r\n                        data={sub}\r\n                        disabled={sub.disabled}\r\n                        onClick={(\r\n                          sub.actionType == eCtxTypeId.ctx_18_set_figure_tag || \r\n                          sub.actionType == eCtxTypeId.ctx_3_color_txt || \r\n                          sub.actionType == eCtxTypeId.ctx_42_set_icon || \r\n                          sub.actionType == eCtxTypeId.ctx_46_text_font ||\r\n                          sub.actionType == eCtxTypeId.ctx_61_flag_color ||\r\n                          sub.actionType == eCtxTypeId.ctx_62_flag_img\r\n                          ) ? () => {} : onClick\r\n                        }\r\n                        style={sub.type != eCtxOptionType.eGroup ? { paddingLeft: \"24px\" } : {}}\r\n                        className={ (sub.actionType == eCtxTypeId.ctx_18_set_figure_tag || \r\n                          sub.actionType == eCtxTypeId.ctx_3_color_txt || \r\n                          sub.actionType == eCtxTypeId.ctx_42_set_icon || \r\n                          sub.actionType == eCtxTypeId.ctx_46_text_font ||\r\n                          sub.actionType == eCtxTypeId.ctx_61_flag_color ||\r\n                          sub.actionType == eCtxTypeId.ctx_62_flag_img\r\n                          ) ? \"context-item-not-focus\" : \"\" }\r\n                      >\r\n                        {\r\n                          sub.actionType == eCtxTypeId.ctx_38_create ?\r\n                          <AppNodeResourceIcon nodeType={sub.colorType} className=\"fontsize-12 fontcolor-normal marginRight-5\" style={{lineHeight: \"24px\"}}/>: \r\n                          sub.actionType == eCtxTypeId.ctx_12_create_shortcut ?\r\n                          (getCtxIconByType(sub.colorType) && <span className={\"iconfont \" + getCtxIconByType(sub.colorType) + \" fontsize-12 fontcolor-normal marginRight-5\"}></span>) :\r\n                          <span></span>\r\n                        }\r\n                        <div style={{ width: \"100%\" }}>{sub.name}</div>\r\n                        {/* 对象状态图标 */}\r\n                        {\r\n                          <span className = {`iconfont ${getMenuStatusIcon(sub.menuStatus).icon} fontsize-14 `}\r\n                           style={{color: getMenuStatusIcon(sub.menuStatus).iconColor}}\r\n                           title={sub.menuStatus == eMenuStatus.status_1_Free_QuotaExceed ?\r\n                               '应用免费额度已用完' : sub.menuStatus == eMenuStatus.status_3_Vip_Unauthorized ?\r\n                                                '应用未对您授权' : sub.menuStatus == eMenuStatus.status_4_Vip_Expired ?\r\n                                                               '应用已过期' : ''}>\r\n                          </span>\r\n                        }\r\n                        <span></span>\r\n                      </CtxMenuItem>\r\n                    </div>\r\n                  );\r\n                })\r\n              )}\r\n            </div>\r\n          </DelayedCtxSubMenu>\r\n        } else {\r\n          return <CtxMenuItem key={key} id={key} onClick={onClick} >\r\n            {el.actionType == 28 ?\r\n            <span className={\"iconfont \" + getCtxIconByType(el.actionType) + ' fontsize-12 fontcolor-normal'} style={{marginLeft:-5,marginRight:5}}></span>\r\n            :\r\n            <span className={\"iconfont \" + getCtxIconByType(el.actionType) + \" fontsize-12 fontcolor-normal marginRight-5\"}></span>\r\n            }\r\n            <div style={{ width: \"100%\" }}>{el.name}</div>\r\n          </CtxMenuItem>\r\n        }\r\n      })}\r\n\r\n    </CtxMenu>\r\n  )\r\n}\r\n\r\n\r\nfunction ContextLoadingBoard() {\r\n  return <React.Fragment>\r\n    <CtxMenuItem disabled>\r\n      <Skeleton.Input active={true} style={{height:\"24px\",overflow:\"hidden\"}}/>\r\n    </CtxMenuItem>\r\n    <CtxMenuItem disabled>\r\n      <Skeleton.Input active={true} style={{height:\"24px\",overflow:\"hidden\"}}/>\r\n    </CtxMenuItem>\r\n    <CtxMenuItem disabled>\r\n      <Skeleton.Input active={true} style={{height:\"24px\",overflow:\"hidden\"}}/>\r\n    </CtxMenuItem>\r\n    <CtxMenuItem disabled>\r\n      <Skeleton.Input active={true} style={{height:\"24px\",overflow:\"hidden\"}}/>\r\n    </CtxMenuItem>\r\n    <CtxMenuItem disabled>\r\n      <Skeleton.Input active={true} style={{height:\"24px\",overflow:\"hidden\"}}/>\r\n    </CtxMenuItem>\r\n  </React.Fragment>\r\n}\r\n\r\n\r\nexport default forwardRef(ContextBoard)\r\n\r\n"], "mappings": ";;;AAAA,SAASA,aAAa,EAAEC,aAAa,QAAQ,mBAAmB;AAChE,SAASC,6BAA6B,QAAQ,kBAAkB;AAChE,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,SAASC,qCAAqC,EAAEC,mCAAmC,QAAQ,6BAA6B;AACxH,SAASC,OAAO,EAAEC,WAAW,EAAEC,cAAc,EAAEC,aAAa,QAAQ,0BAA0B;AAC9F,SAASC,cAAc,EAAEC,UAAU,EAAEC,UAAU,QAAQ,oBAAoB;AAC3E,SAASC,UAAU,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,iBAAiB,QAAQ,yBAAyB;AACzH,SAASC,YAAY,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,yBAAyB;AACpF,SAASC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,MAAM;AACvD,OAAOC,KAAK,IAAIC,UAAU,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3F,SAASC,IAAI,IAAIC,WAAW,EAAEC,IAAI,IAAIC,OAAO,EAAEC,OAAO,IAAIC,UAAU,EAAEC,cAAc,EAAEC,SAAS,QAAQ,iBAAiB;AACxH,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAO,oCAAoC;AAC3C,SAASC,WAAW,QAAQ,uBAAuB;AACnD,OAAO,qBAAqB;AAC5B,OAAO,KAAKC,KAAK,MAAM,kBAAkB;AACzC,SAASC,+BAA+B,QAAQ,iCAAiC;AACjF,SAASC,UAAU,QAAQ,0BAA0B;;AAGrD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,SAASC,iBAAiBA,CAAC;EAAEC,WAAW;EAAEC,QAAQ;EAAEC,gBAAgB;EAAEC;AAAW,CAAC,EAAE;EAClF,MAAMC,UAAU,GAAIC,YAAY,IAAK;IACnC,IAAIC,KAAK,GAAGD,YAAY,CAACE,MAAM,GAAGF,YAAY,CAACA,YAAY,CAACE,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,EAAC;IAC7EN,QAAQ,IAAIA,QAAQ,CAAC;MAAEO,EAAE,EAAEL,UAAU,GAAG,GAAG,GAAGG;IAAM,CAAC,CAAC;EACxD,CAAC;EACD,oBAAOV,OAAA;IAAKa,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAe,CAAE;IAAAC,QAAA,eAC7Cf,OAAA,CAACxB,QAAQ,CAACwC,KAAK;MAACN,KAAK,EAAE,CAACN,WAAW,CAAE;MAACC,QAAQ,EAAEG,UAAW;MAAAO,QAAA,EACxDT,gBAAgB,CAACW,GAAG,CAACC,IAAI,iBAAIlB,OAAA,CAACxB,QAAQ;QAAC2C,IAAI,EAAC,KAAK;QAA4BT,KAAK,EAAEQ,IAAI,CAACE,IAAI,CAACC,QAAQ,CAAC,CAAE;QAACC,SAAS,EAAEJ,IAAI,CAACI;MAAU,GAA7EJ,IAAI,CAACE,IAAI,CAACC,QAAQ,CAAC,CAAC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAqE,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CAAC;AACR;;AAEA;AAAAC,EAAA,GAZSxB,iBAAiB;AAa1B,SAASyB,iBAAiBA,CAAC;EAAExB,WAAW;EAAEC,QAAQ;EAAEC,gBAAgB;EAAEC;AAAW,CAAC,EAAE;EAClF,MAAMC,UAAU,GAAIC,YAAY,IAAK;IACnC,IAAIC,KAAK,GAAGD,YAAY,CAACE,MAAM,GAAGF,YAAY,CAACA,YAAY,CAACE,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,EAAC;IAC7EN,QAAQ,IAAIA,QAAQ,CAAC;MAAEO,EAAE,EAAEL,UAAU,GAAG,GAAG,GAAGG;IAAM,CAAC,CAAC;EACxD,CAAC;EACD,oBAAOV,OAAA;IAAKa,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAe,CAAE;IAAAC,QAAA,eAC7Cf,OAAA,CAACxB,QAAQ,CAACwC,KAAK;MAACN,KAAK,EAAE,CAACN,WAAW,CAAE;MAACC,QAAQ,EAAEG,UAAW;MAAAO,QAAA,EACxDT,gBAAgB,CAACW,GAAG,CAACC,IAAI,iBAAIlB,OAAA,CAACxB,QAAQ;QAAC2C,IAAI,EAAC,KAAK;QAA4BT,KAAK,EAAEQ,IAAI,CAACE,IAAI,CAACC,QAAQ,CAAC,CAAE;QAACC,SAAS,EAAEJ,IAAI,CAACI,SAAU;QAAAP,QAAA,EACnIX,WAAW,IAAIc,IAAI,CAACE,IAAI,GAAG,EAAE,GAAGF,IAAI,CAACW;MAAK,GADYX,IAAI,CAACE,IAAI,CAACC,QAAQ,CAAC,CAAC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAElE,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CAAC;AACR;;AAEA;AAAAI,GAAA,GAdSF,iBAAiB;AAe1B,SAASG,gBAAgBA,CAAC;EAAE1B,QAAQ;EAAE2B,UAAU;EAAEzB;AAAW,CAAC,EAAE;EAC9D,MAAMC,UAAU,GAAIC,YAAY,IAAK;IACnCJ,QAAQ,IAAIA,QAAQ,CAAC;MAAEO,EAAE,EAAEL,UAAU,GAAG,GAAG,GAAGE;IAAa,CAAC,CAAC;EAC/D,CAAC;EACD,oBAAOT,OAAA,CAACvB,KAAK;IAACwD,IAAI,EAAE,EAAG;IAAAlB,QAAA,EACpBiB,UAAU,CAACf,GAAG,CAAEiB,MAAM,iBACrBlC,OAAA;MAAwBmC,OAAO,EAAEA,CAAA,KAAI3B,UAAU,CAAC0B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEd,IAAI,CAAE;MAACE,SAAS,EAAEY,MAAM,CAACZ,SAAU;MAAAP,QAAA,EACzFrB,SAAS,CAACwC,MAAM,CAACE,IAAI;IAAC,GADdF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEd,IAAI;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEjB,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AACV;;AAEA;AAAAW,GAAA,GAbSN,gBAAgB;AAczB,SAASO,gBAAgBA,CAAC;EAAEjC,QAAQ;EAAE2B,UAAU;EAAEzB;AAAW,CAAC,EAAE;EAC9D,MAAMC,UAAU,GAAIC,YAAY,IAAK;IACnCJ,QAAQ,IAAIA,QAAQ,CAAC;MAAEO,EAAE,EAAEL,UAAU,GAAG,GAAG,GAAGE;IAAa,CAAC,CAAC;EAC/D,CAAC;EACD,oBAAOT,OAAA,CAACvB,KAAK;IAACwD,IAAI,EAAE,EAAG;IAAAlB,QAAA,EACpBiB,UAAU,CAACf,GAAG,CAAEiB,MAAM,iBACrBlC,OAAA;MAAwBmC,OAAO,EAAEA,CAAA,KAAI3B,UAAU,CAAC0B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAExB,KAAK,CAAE;MAACY,SAAS,EAAEY,MAAM,CAACZ,SAAU;MAAAP,QAAA,EAC1FrB,SAAS,CAACwC,MAAM,CAACE,IAAI;IAAC,GADdF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEd,IAAI;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEjB,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAAAa,GAAA,GA/CSD,gBAAgB;AAgDzB,MAAME,YAAY,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,KAAK;EAC3C,oBAAO3C,OAAA;IAAKa,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAE8B,cAAc,EAAE,eAAe;MAAEC,UAAU,EAAE,QAAQ;MAAEF;IAAM,CAAE;IAAA5B,QAAA,gBACnGf,OAAA;MAAAe,QAAA,EAAO2B;IAAK;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,EACnBe,IAAI,gBAAGzC,OAAA,CAAC7C,aAAa;MAAC0D,KAAK,EAAE;QAAE8B,KAAK,EAAE;MAAU;IAAE;MAAApB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAG1B,OAAA,CAAAE,SAAA,mBAAI,CAAC;EAAA;IAAAqB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3D,CAAC;AACR,CAAC;;AAED;AACA,MAAMoB,iBAAiB,GAAGA,CAAC;EAAE/B,QAAQ;EAAE2B,KAAK;EAAEK,KAAK;EAAE,GAAGC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAMC,UAAU,GAAGnE,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM,CAACoE,SAAS,EAAEC,YAAY,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMqE,OAAO,GAAGtE,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMuE,UAAU,GAAGvE,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMwE,aAAa,GAAGxE,MAAM,CAAC,KAAK,CAAC;EACnC,MAAM,CAACyE,eAAe,EAAEC,kBAAkB,CAAC,GAAGzE,QAAQ,CAAC;IAAE0E,GAAG,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAO,CAAC,CAAC;EAChF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG7E,QAAQ,CAAC,CAAC,CAAC;;EAErD;EACA,MAAM8E,wBAAwB,GAAGA,CAAA,KAAM;IACrC;IACA,MAAMC,SAAS,GAAGnF,KAAK,CAACoF,QAAQ,CAACC,OAAO,CAAClD,QAAQ,CAAC;IAClD,IAAImD,SAAS,GAAG,CAAC;;IAEjB;IACA,MAAMC,UAAU,GAAIC,KAAK,IAAK;MAC5BA,KAAK,CAACC,OAAO,CAACnD,IAAI,IAAI;QACpB,iBAAItC,KAAK,CAAC0F,cAAc,CAACpD,IAAI,CAAC,EAAE;UAC9B,IAAIA,IAAI,CAAC8B,KAAK,IAAI9B,IAAI,CAAC8B,KAAK,CAACjC,QAAQ,EAAE;YACrC,IAAIwD,KAAK,CAACC,OAAO,CAACtD,IAAI,CAAC8B,KAAK,CAACjC,QAAQ,CAAC,EAAE;cACtCoD,UAAU,CAACjD,IAAI,CAAC8B,KAAK,CAACjC,QAAQ,CAAC;YACjC,CAAC,MAAM;cACLmD,SAAS,EAAE;YACb;UACF,CAAC,MAAM;YACLA,SAAS,EAAE;UACb;QACF;MACF,CAAC,CAAC;IACJ,CAAC;IAEDC,UAAU,CAACJ,SAAS,CAAC;;IAErB;IACA,MAAMU,WAAW,GAAG,EAAE,CAAC,CAAC;IACxB;IACA,MAAMC,YAAY,GAAG,EAAE,CAAC,CAAC;;IAEzB,OAAOC,IAAI,CAACC,GAAG,CAACV,SAAS,GAAGO,WAAW,GAAGC,YAAY,EAAEG,MAAM,CAACC,WAAW,GAAG,EAAE,CAAC,CAAC,CAAC;EACpF,CAAC;;EAED;EACA7F,SAAS,CAAC,MAAM;IACd,IAAI,CAACqE,UAAU,CAACyB,OAAO,EAAE;IAEzB,MAAMC,cAAc,GAAG,IAAIC,cAAc,CAACC,OAAO,IAAI;MACnD,KAAK,IAAIC,KAAK,IAAID,OAAO,EAAE;QACzBrB,gBAAgB,CAACsB,KAAK,CAACC,WAAW,CAACC,MAAM,CAAC;MAC5C;IACF,CAAC,CAAC;IAEFL,cAAc,CAACM,OAAO,CAAChC,UAAU,CAACyB,OAAO,CAAC;IAE1C,OAAO,MAAM;MACXC,cAAc,CAACO,UAAU,CAAC,CAAC;IAC7B,CAAC;EACH,CAAC,EAAE,CAACpC,SAAS,CAAC,CAAC;EAEf,MAAMqC,WAAW,GAAGA,CAAA,KAAM;IACxBjC,aAAa,CAACwB,OAAO,GAAG,IAAI;IAC5B,IAAI7B,UAAU,CAAC6B,OAAO,EAAE;MACtBU,YAAY,CAACvC,UAAU,CAAC6B,OAAO,CAAC;IAClC;IAEA3B,YAAY,CAAC,IAAI,CAAC;;IAElB;IACAsC,UAAU,CAAC,MAAM;MACf,IAAIrC,OAAO,CAAC0B,OAAO,IAAIzB,UAAU,CAACyB,OAAO,EAAE;QACzC,MAAMY,QAAQ,GAAGtC,OAAO,CAAC0B,OAAO,CAACa,qBAAqB,CAAC,CAAC;QACxD,MAAMC,WAAW,GAAGvC,UAAU,CAACyB,OAAO,CAACa,qBAAqB,CAAC,CAAC;QAC9D,MAAME,YAAY,GAAGjB,MAAM,CAACC,WAAW;QACvC,MAAMiB,WAAW,GAAGlB,MAAM,CAACmB,UAAU;;QAErC;QACA,MAAMC,YAAY,GAAGJ,WAAW,CAACR,MAAM,GAAG,CAAC,GAAGQ,WAAW,CAACR,MAAM,GAAGvB,wBAAwB,CAAC,CAAC;QAC7F,MAAMoC,YAAY,GAAGL,WAAW,CAACM,KAAK,GAAG,CAAC,GAAGN,WAAW,CAACM,KAAK,GAAG,GAAG,CAAC,CAAC;;QAEtE;QACA,IAAIC,WAAW,GAAG,CAAC,EAAE;QACrB,IAAIC,YAAY,GAAG,MAAM;;QAEzB;QACA,MAAMC,cAAc,GAAGX,QAAQ,CAACjC,GAAG,GAAGuC,YAAY,GAAGH,YAAY;QACjE,IAAIQ,cAAc,GAAG,CAAC,EAAE;UACtB;UACAF,WAAW,GAAGzB,IAAI,CAAC4B,GAAG,CAAC,CAACZ,QAAQ,CAACjC,GAAG,GAAG,EAAE,EAAE,CAAC4C,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;QACpE;;QAEA;QACA,MAAME,aAAa,GAAGb,QAAQ,CAACc,KAAK,GAAGP,YAAY,GAAGH,WAAW;QACjE,IAAIS,aAAa,GAAG,CAAC,EAAE;UACrB;UACAH,YAAY,GAAG,IAAIH,YAAY,IAAI;UACnC;UACA,IAAI5C,UAAU,CAACyB,OAAO,EAAE;YACtBzB,UAAU,CAACyB,OAAO,CAAC2B,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;UAC7D;QACF,CAAC,MAAM;UACL;UACA,IAAIrD,UAAU,CAACyB,OAAO,EAAE;YACtBzB,UAAU,CAACyB,OAAO,CAAC2B,SAAS,CAACE,MAAM,CAAC,yBAAyB,CAAC;UAChE;QACF;QAEAnD,kBAAkB,CAAC;UAAEC,GAAG,EAAE0C,WAAW;UAAEzC,IAAI,EAAE0C;QAAa,CAAC,CAAC;MAC9D;IACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EACV,CAAC;EAED,MAAMQ,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI3D,UAAU,CAAC6B,OAAO,EAAE;MACtBU,YAAY,CAACvC,UAAU,CAAC6B,OAAO,CAAC;IAClC;IACA7B,UAAU,CAAC6B,OAAO,GAAGW,UAAU,CAAC,MAAM;MACpC,IAAI,CAACnC,aAAa,CAACwB,OAAO,EAAE;QAC1B3B,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;;EAED;EACA,MAAM0D,WAAW,GAAIC,IAAI,IAAK;IAC5B,IAAI;MACF,OAAOA,IAAI,IAAKA,IAAI,YAAYC,IAAK;IACvC,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B1B,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAM2B,gBAAgB,GAAIF,CAAC,IAAK;IAAA,IAAAG,gBAAA;IAE9B;IACA,IAAI9D,UAAU,CAACyB,OAAO,IAAI+B,WAAW,CAACG,CAAC,CAACI,aAAa,CAAC,IAAI/D,UAAU,CAACyB,OAAO,CAACuC,QAAQ,CAACL,CAAC,CAACI,aAAa,CAAC,EAAE;MACtG;IACF;;IAEA;IACA,MAAME,UAAU,IAAAH,gBAAA,GAAG/D,OAAO,CAAC0B,OAAO,cAAAqC,gBAAA,uBAAfA,gBAAA,CAAiBI,aAAa;IACjD,IAAID,UAAU,IAAIT,WAAW,CAACG,CAAC,CAACI,aAAa,CAAC,IAAIE,UAAU,CAACD,QAAQ,CAACL,CAAC,CAACI,aAAa,CAAC,EAAE;MACtF;IACF;IAEA9D,aAAa,CAACwB,OAAO,GAAG,KAAK;IAC7B8B,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMY,uBAAuB,GAAGA,CAAA,KAAM;IACpCjC,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMkC,uBAAuB,GAAIT,CAAC,IAAK;IAAA,IAAAU,iBAAA;IAErC;IACA,IAAItE,OAAO,CAAC0B,OAAO,IAAI+B,WAAW,CAACG,CAAC,CAACI,aAAa,CAAC,IAAIhE,OAAO,CAAC0B,OAAO,CAACuC,QAAQ,CAACL,CAAC,CAACI,aAAa,CAAC,EAAE;MAChG;IACF;;IAEA;IACA,MAAME,UAAU,IAAAI,iBAAA,GAAGtE,OAAO,CAAC0B,OAAO,cAAA4C,iBAAA,uBAAfA,iBAAA,CAAiBH,aAAa;IACjD,IAAID,UAAU,IAAIT,WAAW,CAACG,CAAC,CAACI,aAAa,CAAC,IAAIE,UAAU,CAACD,QAAQ,CAACL,CAAC,CAACI,aAAa,CAAC,EAAE;MACtF;IACF;IAEA9D,aAAa,CAACwB,OAAO,GAAG,KAAK;IAC7B8B,WAAW,CAAC,CAAC;EACf,CAAC;EAEDjI,KAAK,CAACK,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACX,IAAIiE,UAAU,CAAC6B,OAAO,EAAE;QACtBU,YAAY,CAACvC,UAAU,CAAC6B,OAAO,CAAC;MAClC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE/E,OAAA;IACE4H,GAAG,EAAEvE,OAAQ;IACbwE,YAAY,EAAEX,gBAAiB;IAC/BY,YAAY,EAAEX,gBAAiB;IAC/B7F,SAAS,EAAC,2BAA2B;IACrCT,KAAK,EAAE;MAAEkH,QAAQ,EAAE;IAAW,CAAE;IAAAhH,QAAA,eAEhCf,OAAA,CAACT,UAAU;MAAA,GACLyD,KAAK;MACTN,KAAK,EAAEA,KAAM;MACbK,KAAK,EAAEA,KAAM;MACblC,KAAK,EAAE;QACL,GAAGmC,KAAK,CAACnC,KAAK;QACdkH,QAAQ,EAAE,UAAU;QACpBpE,IAAI,EAAEH,eAAe,CAACG,IAAI;QAC1BD,GAAG,EAAEF,eAAe,CAACE,GAAG;QACxBsE,MAAM,EAAE,GAAG;QACXlH,OAAO,EAAEqC,SAAS,GAAG,OAAO,GAAG,MAAM;QACrC8E,SAAS,EAAE,oBAAoB;QAAE;QACjCC,SAAS,EAAE,MAAM,CAAC;MACpB,CAAE;MAAAnH,QAAA,eAEFf,OAAA;QACE4H,GAAG,EAAEtE,UAAW;QAChBuE,YAAY,EAAEJ,uBAAwB;QACtCK,YAAY,EAAEJ,uBAAwB;QACtC7G,KAAK,EAAE;UACLsF,KAAK,EAAE,MAAM;UACbd,MAAM,EAAE,MAAM;UACd0C,QAAQ,EAAE,UAAU;UACpBC,MAAM,EAAE;QACV,CAAE;QAAAjH,QAAA,EAEDA;MAAQ;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPAuB,EAAA,CA9NMH,iBAAiB;AAAAqF,GAAA,GAAjBrF,iBAAiB;AAsOvB,SAASsF,YAAYA,CAAC;EAAEC,MAAM;EAAEC,cAAc;EAAEC,gBAAgB;EAAE3H,EAAE;EAAE4H,wBAAwB;EAAE,GAAGC;AAAe,CAAC,EAAEb,GAAG,EAAE;EAAAc,GAAA;EACxH;EACA;EACA,MAAM;IAAEC,IAAI,EAAE;MAAEC,MAAM;MAAEC,aAAa,EAAEC;IAAY,CAAC,GAAG;MAACF,MAAM,EAAEG,SAAS;MAAEF,aAAa,EAAEE;IAAS;EAAE,CAAC,GAClGxL,qCAAqC,CAAC;IAAC8K,MAAM;IAAEW,OAAO,EAAE;EAAI,CAAC,CAAC,CAAC,CAAC;EACpE,MAAM;IAAEL,IAAI,EAAEM;EAAc,CAAC,GAAGzL,mCAAmC,CAAC6K,MAAM,CAAC,CAAC,CAAC;;EAE7E,MAAM;IAACa,SAAS,EAACC,YAAY;IAAER,IAAI,EAACS,cAAc,GAAC,EAAE;IAAEC;EAAW,CAAC,GAAG1J,WAAW,CAAC;IAChF2J,UAAU,EAAEA,CAAC;MAACC,QAAQ;MAACC,MAAM;MAACC,iBAAiB;MAACC,aAAa;MAAEC;IAAQ,CAAC,KAAKC,mBAAmB,CAACL,QAAQ,EAACC,MAAM,EAACC,iBAAiB,EAACC,aAAa,EAAEC,QAAQ;EAC5J,CAAC,CAAC;EAEF,MAAME,kBAAkB,GAAGlK,WAAW,CAAC;IACrC2J,UAAU,EAAE1J,KAAK,CAACkK;EACpB,CAAC,CAAC;;EAEF;EACA,MAAMC,qBAAqB,GAAIC,SAAS,IAAK;IAC3C,IAAG,CAAC,CAACA,SAAS,EAAE;MACdH,kBAAkB,CAACI,MAAM,CAAC;QAAE5B,MAAM;QAAE2B;MAAU,CAAC,EAAE;QAC/CE,SAAS,EAAGC,MAAM,IAAK;UACrB,IAAGA,MAAM,CAACC,UAAU,KAAK,GAAG,EAAE;YAC5B;YACAzL,KAAK,CAAC0L,IAAI,CAAC;cACTxI,KAAK,EAAE,IAAI;cACXyI,OAAO,EAAE,yBAAyB;cAClCC,YAAY,EAAE,IAAI;cAClB;cACAC,MAAM,EAAE,MAAM;cACdrE,KAAK,EAAE;YACT,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMsE,QAAQ,GAAG1L,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3B,MAAM;IAAE2L;EAAK,CAAC,GAAGlL,cAAc,CAAC;IAAEoB,EAAE,EAAEA;EAAG,CAAC,CAAC;EAE3C9B,mBAAmB,CAAC8I,GAAG,EAAE,OAAO;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA+C,gBAAgB,EAAEA,CAAC1D,CAAC,EAAEsC,QAAQ,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,QAAQ,KAAKiB,OAAO,CAAC3D,CAAC,EAAEsC,QAAQ,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,QAAQ;EAChK,CAAC,CAAC,CAAC;;EAEH;EACA;EACA,MAAMiB,OAAO,GAAG,MAAAA,CAAO3D,CAAC,EAAEsC,QAAQ,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,QAAQ,KAAK;IACzF;IACA,MAAMkB,UAAU,GAAG,GAAG,CAAC,CAAC;IACxB,MAAMC,SAAS,GAAG,GAAG,CAAC,CAAC;IACvB,MAAMhF,YAAY,GAAGjB,MAAM,CAACC,WAAW;IACvC,MAAMiB,WAAW,GAAGlB,MAAM,CAACmB,UAAU;IACrC,MAAM+E,MAAM,GAAG9D,CAAC,CAAC+D,OAAO;IACxB,MAAMC,MAAM,GAAGhE,CAAC,CAACiE,OAAO;;IAExB;IACA,MAAMC,SAAS,GAAGJ,MAAM,GAAGF,UAAU,GAAG/E,YAAY,GAClDnB,IAAI,CAAC4B,GAAG,CAAC,CAAC,EAAET,YAAY,GAAG+E,UAAU,GAAG,EAAE,CAAC,GAC3CE,MAAM;;IAER;IACA,MAAMK,SAAS,GAAGH,MAAM,GAAGH,SAAS,GAAG/E,WAAW,GAChDpB,IAAI,CAAC4B,GAAG,CAAC,CAAC,EAAER,WAAW,GAAG+E,SAAS,GAAG,EAAE,CAAC,GACzCG,MAAM;;IAER;IACAP,IAAI,CAAC;MACHW,KAAK,EAAEpE,CAAC;MACRjE,KAAK,EAAEuG,QAAQ;MACfxB,QAAQ,EAAE;QACRuD,CAAC,EAAEF,SAAS;QACZG,CAAC,EAAEJ;MACL;IACF,CAAC,CAAC;IAEF9B,WAAW,CAAC;MAACE,QAAQ;MAAEC,MAAM;MAAEC,iBAAiB;MAAEC,aAAa;MAAEC;IAAQ,CAAC,CAAC;IAC3Ec,QAAQ,CAAC1F,OAAO,CAACwE,QAAQ,GAAGA,QAAQ;EACtC,CAAC;;EAED;EACA;EACA,MAAMK,mBAAmB,GAAG,MAAAA,CAAOL,QAAQ,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,QAAQ,KAAK;IAClG,IAAIQ,MAAM,GAAG,CAAC1M,OAAO,CAACiM,aAAa,CAAC,GAAG;MAAEU,UAAU,EAAE,GAAG;MAAEV;IAAc,CAAC,GAAG,MAAMrM,6BAA6B,CAAC;MAAEgL,MAAM;MAAEmB,MAAM;MAAEG;IAAQ,CAAC,CAAC;IAC5I,IAAIQ,MAAM,CAACC,UAAU,IAAI,GAAG,EAAE;MAC5B,IAAI;QAACV,aAAa;QAAE8B;MAAW,CAAC,GAAGrB,MAAM;MACzC,IAAG,CAAC1M,OAAO,CAACgM,iBAAiB,CAAC,EAAEC,aAAa,GAAGA,aAAa,CAAC+B,MAAM,CAACC,SAAS,IAAI,CAAC,CAACjC,iBAAiB,IAAI,EAAE,EAAEkC,IAAI,CAACpL,UAAU,IAAImL,SAAS,CAACnL,UAAU,IAAIA,UAAU,CAAC,CAAC,EAAC;MACrKmJ,aAAa,CAACrF,OAAO,CAACqH,SAAS,IAAI;QACjC,QAAQ,CAAEA,SAAS,CAACnL,UAAU;UAC5B,KAAKvC,UAAU,CAAC4N,aAAa;YAAE;YAC7BF,SAAS,CAAC3K,QAAQ,GAAG8K,sBAAsB,CAACH,SAAS,CAAC3K,QAAQ,CAAC;YAC/D;UACF,KAAK/C,UAAU,CAAC8N,sBAAsB;YAAE;YACtCJ,SAAS,CAAC3K,QAAQ,GAAGgL,2BAA2B,CAACL,SAAS,CAAC3K,QAAQ,EAAEwI,QAAQ,CAAC;YAC9E;UACF,KAAKvL,UAAU,CAACgO,gBAAgB;YAAE;YAChCN,SAAS,CAAC3K,QAAQ,GAAGkL,YAAY,CAACP,SAAS,CAAC3K,QAAQ,EAAEwI,QAAQ,CAAC;YAC/D;UACF,KAAKvL,UAAU,CAACkO,eAAe;YAAE;YAC/B,MAAMC,YAAY,GAAGX,WAAW,IAAI1N,UAAU,CAACsO,MAAM;YACrDV,SAAS,CAACW,SAAS,GAAGF,YAAY,GAAGrO,UAAU,CAACwO,OAAO,GAAGxO,UAAU,CAACsO,MAAM;YAC3EV,SAAS,CAACvK,IAAI,GAAGqB,YAAY,CAAC2J,YAAY,EAAET,SAAS,CAACvK,IAAI,CAAC;YAC3D;UACF,KAAKnD,UAAU,CAACuO,UAAU;YAAE;YAC1B,MAAMC,OAAO,GAAGjD,QAAQ,CAACkD,MAAM,IAAI3O,UAAU,CAACsO,MAAM;YACpDV,SAAS,CAACW,SAAS,GAAGG,OAAO,GAAG1O,UAAU,CAACwO,OAAO,GAAGxO,UAAU,CAACsO,MAAM;YACtEV,SAAS,CAACvK,IAAI,GAAGqB,YAAY,CAACgK,OAAO,EAAEd,SAAS,CAACvK,IAAI,CAAC;YACtD;UACF,KAAKnD,UAAU,CAAC0O,cAAc;YAAE;YAC9B,MAAMC,QAAQ,GAAGpD,QAAQ,CAACqD,OAAO,IAAI9O,UAAU,CAACsO,MAAM;YACtDV,SAAS,CAACW,SAAS,GAAGM,QAAQ,GAAG7O,UAAU,CAACwO,OAAO,GAAGxO,UAAU,CAACsO,MAAM;YACvEV,SAAS,CAACvK,IAAI,GAAGqB,YAAY,CAACmK,QAAQ,EAAEjB,SAAS,CAACvK,IAAI,CAAC;YACvD;UACF;YACEuK,SAAS,CAAC3K,QAAQ,GAAG2K,SAAS,CAAC3K,QAAQ,CAACE,GAAG,CAAE4L,IAAI,KAAI;cAAC,GAAGA,IAAI;cAAEtM,UAAU,EAAEmL,SAAS,CAACnL,UAAU;cAAE8L,SAAS,EAAEQ,IAAI,CAACtM;YAAU,CAAC,CAAC,CAAC;YAC9H;QACJ;MACF,CAAC,CAAC;MACF,OAAOmJ,aAAa;IACtB,CAAC,MAAM;MACL,OAAO,EAAE;IACX;EACF,CAAC;;EAID;EACA,MAAMmC,sBAAsB,GAAIzC,cAAc,IAAK;IACjD,IAAI0D,eAAe,GAAGlP,aAAa,CAACwL,cAAc,EAAE,KAAK,CAAC;IAC1D0D,eAAe,GAAGpP,WAAW,CAACoP,eAAe,CAAC;IAC9CA,eAAe,CAACzI,OAAO,CAAE0I,WAAW,IAAG;MACrCA,WAAW,CAACV,SAAS,GAAGU,WAAW,CAACxM,UAAU;MAC9CwM,WAAW,CAACxM,UAAU,GAAGvC,UAAU,CAAC4N,aAAa;IACnD,CAAC,CAAC;IACF,OAAOkB,eAAe;EACxB,CAAC;;EAED;EACA,MAAMf,2BAA2B,GAAGA,CAACiB,mBAAmB,EAAEzD,QAAQ,KAAK;IACrE,IAAI0D,oBAAoB,GAAGrP,aAAa,CAACoP,mBAAmB,EAAE,IAAI,CAAC;IACnEC,oBAAoB,GAAGA,oBAAoB,CAAChM,GAAG,CAAEiM,gBAAgB,IAAG;MAClE,IAAGA,gBAAgB,CAAC3M,UAAU,IAAIvC,UAAU,CAACmP,qBAAqB,EAAC;QAAA,IAAAC,qBAAA;QAAE;QACnE,MAAMC,gBAAgB,GAAG,CAACH,gBAAgB,CAACnM,QAAQ,IAAI,EAAE,EAAEE,GAAG,CAACqM,MAAM,IAAI;UACvE,IAAI;YACF,OAAOC,IAAI,CAACC,KAAK,CAACF,MAAM,CAACnM,IAAI,CAAC;UAChC,CAAC,CAAC,OAAOsM,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEH,MAAM,CAACnM,IAAK,CAAC;YACrD,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;QACF+L,gBAAgB,CAACnM,QAAQ,GAAG,CAC1B;UACER,UAAU,EAAEvC,UAAU,CAACmP,qBAAqB;UAC5ChM,IAAI,eAAEnB,OAAA,CAACG,iBAAiB;YAACC,WAAW,GAAAgN,qBAAA,GAAE7D,QAAQ,CAACoE,gBAAgB,cAAAP,qBAAA,uBAAzBA,qBAAA,CAA2B/L,QAAQ,CAAC,CAAE;YAAChB,QAAQ,EAAE8B,OAAQ;YAAC7B,gBAAgB,EAAI+M,gBAAiB;YAAC9M,UAAU,EAAEvC,UAAU,CAACmP;UAAsB;YAAA5L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QACtL,CAAC,CACF;QACD,OAAOwL,gBAAgB;MACzB,CAAC,MAAM,IAAGA,gBAAgB,CAAC3M,UAAU,IAAIvC,UAAU,CAAC4P,eAAe,EAAC;QAAA,IAAAC,qBAAA;QAAE;QACpE,MAAMC,gBAAgB,GAAG,CAACZ,gBAAgB,CAACnM,QAAQ,IAAI,EAAE,EAAEE,GAAG,CAACqM,MAAM,IAAI;UACvE,IAAI;YACF,IAAIpL,MAAM,GAAGqL,IAAI,CAACC,KAAK,CAACF,MAAM,CAACnM,IAAI,CAAC;YACpC,OAAOe,MAAM;UACf,CAAC,CAAC,OAAOuL,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEH,MAAM,CAACnM,IAAK,CAAC;YACrD,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;QACH;AACP;AACA;AACA;AACA;AACA;AACA;AACA;QACQuM,OAAO,CAACK,GAAG,CAAC,kBAAkB,EAAED,gBAAgB,CAAC;QACjDZ,gBAAgB,CAACnM,QAAQ,GAAG,CAC1B;UACER,UAAU,EAAEvC,UAAU,CAAC4P,eAAe;UACtCzM,IAAI,eAAEnB,OAAA,CAAC4B,iBAAiB;YAACxB,WAAW,GAAAyN,qBAAA,GAAEtE,QAAQ,CAACyE,iBAAiB,cAAAH,qBAAA,uBAA1BA,qBAAA,CAA4BxM,QAAQ,CAAC,CAAE;YAAChB,QAAQ,EAAE8B,OAAQ;YAAC7B,gBAAgB,EAAIwN,gBAAiB;YAACvN,UAAU,EAAEvC,UAAU,CAAC4P;UAAgB;YAAArM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QACjL,CAAC,CACF;QACD,OAAOwL,gBAAgB;MACzB,CAAC,MAAM,IAAGA,gBAAgB,CAAC3M,UAAU,IAAIvC,UAAU,CAACiQ,gBAAgB,EAAE;QAAA,IAAAC,qBAAA;QAAE;QACtE,IAAIC,mBAAmB,GAAG,CAACjB,gBAAgB,CAACnM,QAAQ,IAAI,EAAE,EAAEE,GAAG,CAACqM,MAAM,IAAI;UACxE,IAAI;YAAA,IAAAc,iBAAA,EAAAC,qBAAA;YACF,IAAInM,MAAM,GAAIqL,IAAI,CAACC,KAAK,CAACF,MAAM,CAACnM,IAAI,CAAC;YACrC,IAAImN,WAAW,GAAG3Q,cAAc,CAACsL,aAAa,CAAC;YAC/C/G,MAAM,CAACE,IAAI,IAAAgM,iBAAA,GAAGE,WAAW,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,QAAQ,IAAIvM,MAAM,CAACxB,KAAK,CAAC,cAAA0N,iBAAA,uBAArDA,iBAAA,CAAuDM,SAAS;YAC9E,MAAMC,oBAAoB,GAAG,EAAAN,qBAAA,GAAA9E,QAAQ,CAACqF,gBAAgB,cAAAP,qBAAA,uBAAzBA,qBAAA,CAA2BQ,KAAK,CAAC,GAAG,CAAC,KAAI,EAAE,CAAC,CAAC;YAC1E3M,MAAM,CAACZ,SAAS,GAAGqN,oBAAoB,CAACvQ,iBAAiB,CAAC8D,MAAM,CAACd,IAAI,CAAC,CAAC0N,GAAG,CAAC,IAAIhR,UAAU,CAACsO,MAAM,GAAG,kBAAkB,GAAG,EAAE;YAC1H,OAAOlK,MAAM;UACf,CAAC,CAAC,OAAOuL,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEH,MAAM,CAACnM,IAAK,CAAC;YACrD,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;QACFuM,OAAO,CAACK,GAAG,CAAC,qBAAqB,EAAEI,mBAAmB,CAAC;QACvDjB,gBAAgB,CAACnM,QAAQ,GAAG,CAC1B;UACER,UAAU,EAAE2M,gBAAgB,CAAC3M,UAAU;UACvCY,IAAI,eAAEnB,OAAA,CAAC+B,gBAAgB;YAAC3B,WAAW,GAAA8N,qBAAA,GAAE3E,QAAQ,CAACwF,YAAY,cAAAb,qBAAA,uBAArBA,qBAAA,CAAuB7M,QAAQ,CAAC,CAAE;YAAChB,QAAQ,EAAE8B,OAAQ;YAACH,UAAU,EAAEmM,mBAAoB;YAAC5N,UAAU,EAAE2M,gBAAgB,CAAC3M;UAAW;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QACvK,CAAC,CACF;QACD,OAAOwL,gBAAgB;MACzB,CAAC,MAAM,IAAGA,gBAAgB,CAAC3M,UAAU,IAAIvC,UAAU,CAACgR,eAAe,EAAC;QAAA,IAAAC,sBAAA;QAAE;QACpE,IAAIC,gBAAgB,GAAG,CAAChC,gBAAgB,CAACnM,QAAQ,IAAI,EAAE,EAAEE,GAAG,CAACqM,MAAM,IAAI;UACrE,IAAI;YAAA,IAAA6B,kBAAA,EAAAC,sBAAA;YACF,IAAIlN,MAAM,GAAIqL,IAAI,CAACC,KAAK,CAACF,MAAM,CAACnM,IAAI,CAAC;YACrC,IAAImN,WAAW,GAAG3Q,cAAc,CAACsL,aAAa,CAAC;YAC/C/G,MAAM,CAACE,IAAI,IAAA+M,kBAAA,GAAGb,WAAW,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,QAAQ,IAAIvM,MAAM,CAACxB,KAAK,CAAC,cAAAyO,kBAAA,uBAArDA,kBAAA,CAAuDT,SAAS;YAC9ExM,MAAM,CAACZ,SAAS,GAAG,CAAC,EAAA8N,sBAAA,GAAA7F,QAAQ,CAACwF,YAAY,cAAAK,sBAAA,uBAArBA,sBAAA,CAAuBP,KAAK,CAAC,GAAG,CAAC,KAAI,EAAE,EAAElD,IAAI,CAAC0D,QAAQ,IAAInN,MAAM,CAACxB,KAAK,IAAI2O,QAAS,CAAC,GAAG,kBAAkB,GAAG,EAAE;YAClI,OAAOnN,MAAM;UACf,CAAC,CAAC,OAAOuL,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEH,MAAM,CAACnM,IAAK,CAAC;YACrD,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;QACF+L,gBAAgB,CAACnM,QAAQ,GAAG,CAC1B;UACER,UAAU,EAAE2M,gBAAgB,CAAC3M,UAAU;UACvCY,IAAI,eAAEnB,OAAA,CAACsC,gBAAgB;YAAClC,WAAW,GAAA6O,sBAAA,GAAE1F,QAAQ,CAACwF,YAAY,cAAAE,sBAAA,uBAArBA,sBAAA,CAAuB5N,QAAQ,CAAC,CAAE;YAAChB,QAAQ,EAAE8B,OAAQ;YAACH,UAAU,EAAEkN,gBAAiB;YAAC3O,UAAU,EAAE2M,gBAAgB,CAAC3M;UAAW;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QACpK,CAAC,CACF;QACD,OAAOwL,gBAAgB;MACzB,CAAC,MAAO;QACN,OAAOA,gBAAgB;MACzB;IACF,CAAC,CAAC;IACFD,oBAAoB,GAAGvP,WAAW,CAACuP,oBAAoB,CAAC;IACxD,OAAOA,oBAAoB;EAC7B,CAAC;;EAED;EACA,MAAMhB,YAAY,GAAGA,CAACe,mBAAmB,EAAEzD,QAAQ,KAAK;IACtD,IAAI0D,oBAAoB,GAAGrP,aAAa,CAACoP,mBAAmB,EAAE,IAAI,CAAC;IACnEC,oBAAoB,GAAGA,oBAAoB,CAAChM,GAAG,CAAEiM,gBAAgB,IAAG;MAClE,IAAGA,gBAAgB,CAAC3M,UAAU,IAAIvC,UAAU,CAACsR,iBAAiB,EAAC;QAAA,IAAAC,kBAAA;QAAE;QAC/D,MAAMlC,gBAAgB,GAAG,CAACH,gBAAgB,CAACnM,QAAQ,IAAI,EAAE,EAAEE,GAAG,CAACqM,MAAM,IAAI;UACvE,IAAI;YACF,OAAOC,IAAI,CAACC,KAAK,CAACF,MAAM,CAACnM,IAAI,CAAC;UAChC,CAAC,CAAC,OAAOsM,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEH,MAAM,CAACnM,IAAK,CAAC;YACrD,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;QACF+L,gBAAgB,CAACnM,QAAQ,GAAG,CAC1B;UACER,UAAU,EAAE2M,gBAAgB,CAAC3M,UAAU;UACvCY,IAAI,eAAEnB,OAAA,CAACG,iBAAiB;YAACC,WAAW,GAAAmP,kBAAA,GAAEhG,QAAQ,CAACiG,QAAQ,cAAAD,kBAAA,uBAAjBA,kBAAA,CAAmBlO,QAAQ,CAAC,CAAE;YAAChB,QAAQ,EAAE8B,OAAQ;YAAC7B,gBAAgB,EAAI+M,gBAAiB;YAAC9M,UAAU,EAAE2M,gBAAgB,CAAC3M;UAAW;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QACzK,CAAC,CACF;QACD,OAAOwL,gBAAgB;MACzB,CAAC,MAAO,IAAGA,gBAAgB,CAAC3M,UAAU,IAAIvC,UAAU,CAACyR,eAAe,EAAC;QAAA,IAAAC,mBAAA;QAAE;QACrE,IAAIR,gBAAgB,GAAG,CAAChC,gBAAgB,CAACnM,QAAQ,IAAI,EAAE,EAAEE,GAAG,CAACqM,MAAM,IAAI;UACrE,IAAI;YAAA,IAAAqC,oBAAA,EAAAC,mBAAA;YACF,IAAI1N,MAAM,GAAIqL,IAAI,CAACC,KAAK,CAACF,MAAM,CAACnM,IAAI,CAAC;YACrCe,MAAM,CAACE,IAAI,IAAAuN,oBAAA,GAAGhS,cAAc,CAACsL,aAAa,CAAC,CAACsF,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,QAAQ,IAAIvM,MAAM,CAACxB,KAAK,CAAC,cAAAiP,oBAAA,uBAAvEA,oBAAA,CAAyEjB,SAAS;YAChGxM,MAAM,CAACZ,SAAS,GAAG,CAAC,EAAAsO,mBAAA,GAAArG,QAAQ,CAACiG,QAAQ,cAAAI,mBAAA,uBAAjBA,mBAAA,CAAmBf,KAAK,CAAC,GAAG,CAAC,KAAI,EAAE,EAAElD,IAAI,CAAC0D,QAAQ,IAAInN,MAAM,CAACxB,KAAK,IAAI2O,QAAS,CAAC,GAAG,kBAAkB,GAAG,EAAE;YAC9H,OAAOnN,MAAM;UACf,CAAC,CAAC,OAAOuL,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEH,MAAM,CAACnM,IAAK,CAAC;YACrD,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;QACF+L,gBAAgB,CAACnM,QAAQ,GAAG,CAC1B;UACER,UAAU,EAAE2M,gBAAgB,CAAC3M,UAAU;UACvCY,IAAI,eAAEnB,OAAA,CAACsC,gBAAgB;YAAClC,WAAW,GAAAsP,mBAAA,GAAEnG,QAAQ,CAACiG,QAAQ,cAAAE,mBAAA,uBAAjBA,mBAAA,CAAmBrO,QAAQ,CAAC,CAAE;YAAChB,QAAQ,EAAE8B,OAAQ;YAACH,UAAU,EAAEkN,gBAAiB;YAAC3O,UAAU,EAAE2M,gBAAgB,CAAC3M;UAAW;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAChK,CAAC,CACF;QACD,OAAOwL,gBAAgB;MACzB,CAAC,MAAO;QACN,OAAOA,gBAAgB;MACzB;IACF,CAAC,CAAC;IACFD,oBAAoB,GAAIA,oBAAoB,CAAC4C,OAAO,CAAEC,SAAS,IAAKA,SAAS,CAAC/O,QAAQ,CAAC;IACvF,OAAOkM,oBAAoB;EAC7B,CAAC;;EAGD;EACA,MAAM9K,OAAO,GAAGA,CAAC;IAAEvB,EAAE;IAAEoC,KAAK;IAAE2F,IAAI;IAAEoH,YAAY;IAAE,GAAGC;EAAK,CAAC,KAAK;IAC9D,IAAIC,GAAG,GAAGrP,EAAE,CAACiO,KAAK,CAAC,GAAG,CAAC,EAAC;IACxB,IAAIqB,YAAY,GAAG;MACjB3P,UAAU,EAAE0P,GAAG,CAAC,CAAC,CAAC;MAClB5D,SAAS,EAAE4D,GAAG,CAAC,CAAC,CAAC;MACjBE,OAAO,EAAExH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwH,OAAO;MACtBnG,SAAS,EAAErB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,SAAS,CAAC;IAC7B,CAAC;IACD0D,OAAO,CAACK,GAAG,CAACmC,YAAY,CAAC;IACzBE,kBAAkB,CAACF,YAAY,CAAC;EAClC,CAAC;EAED,MAAMG,eAAe,GAAIpJ,CAAC,IAAK;IAC7BqB,cAAc,IAAIA,cAAc,CAAC;MAAEgI,QAAQ,EAAE7F,QAAQ,CAAC1F,OAAO,CAACwE,QAAQ;MAAEgH,OAAO,EAAEtJ,CAAC,CAAC1G,UAAU;MAAE8L,SAAS,EAAEpF,CAAC,CAACoF,SAAS;MAAE,GAAG5D;IAAe,CAAC,CAAC;EAC7I,CAAC;EAED,MAAM+H,iBAAiB,GAAIvJ,CAAC,IAAK;IAC/BsB,gBAAgB,IAAIA,gBAAgB,CAAC;MAAE+H,QAAQ,EAAE7F,QAAQ,CAAC1F,OAAO,CAACwE,QAAQ;MAAEI,QAAQ,EAAE1C,CAAC,CAACoF,SAAS;MAAE,GAAG5D;IAAc,CAAC,CAAC;EACxH,CAAC;EAED,MAAM2H,kBAAkB,GAAGA,CAAC;IAAE7P,UAAU;IAAE8L,SAAS;IAAErC;EAAU,CAAC,KAAK;IACnE,IAAIzJ,UAAU,IAAIvC,UAAU,CAAC4N,aAAa,EAAE;MAAE;MAC5C;MACA,MAAM7E,IAAI,GAAG0J,wBAAwB,CAACrH,cAAc,EAAE7I,UAAU,EAAE8L,SAAS,CAAC;MAC5E,IAAGtF,IAAI,CAAC2J,UAAU,IAAIzS,WAAW,CAAC0S,yBAAyB,EAAE;QAAE;QAC7D,OAAOrS,gBAAgB,CAAC+J,MAAM,EAAES,WAAW,EAAE/B,IAAI,CAAC5F,IAAI,EAAE4F,IAAI,CAAC2J,UAAU,EAAE3J,IAAI,CAAC6J,YAAY,EAAE5G,SAAS,CAAC;MACxG;MACA,IAAGjD,IAAI,CAAC2J,UAAU,IAAIzS,WAAW,CAAC4S,yBAAyB,EAAE;QAAE;QAC7D,OAAOtS,UAAU,CAAC8J,MAAM,EAAES,WAAW,EAAE/B,IAAI,CAAC5F,IAAI,EAAE4F,IAAI,CAAC2J,UAAU,EAAE3J,IAAI,CAAC6J,YAAY,EAAE5G,SAAS,EAACD,qBAAqB,CAAC;MACxH;MACA,IAAGhD,IAAI,CAAC2J,UAAU,IAAIzS,WAAW,CAAC6S,oBAAoB,EAAE;QAAE;QACxD,OAAOzS,YAAY,CAACgK,MAAM,EAAES,WAAW,EAAE/B,IAAI,CAAC5F,IAAI,EAAE4F,IAAI,CAAC2J,UAAU,EAAE3J,IAAI,CAAC6J,YAAY,EAAE5G,SAAS,CAAC;MACpG;MACAwG,iBAAiB,CAAC;QAAEnE;MAAU,CAAC,CAAC;IAClC,CAAC,MAAM;MACLgE,eAAe,CAAC;QAAE9P,UAAU;QAAE8L;MAAU,CAAC,CAAC;IAC5C;EACF,CAAC;;EAED;EACA,SAASoE,wBAAwBA,CAACM,IAAI,EAAExQ,UAAU,EAAE8L,SAAS,EAAE;IAC3D,KAAK,IAAI2E,CAAC,IAAID,IAAI,EAAE;MAClB,IAAIA,IAAI,CAACC,CAAC,CAAC,CAACzQ,UAAU,IAAIA,UAAU,IAAIwQ,IAAI,CAACC,CAAC,CAAC,CAAC3E,SAAS,IAAIA,SAAS,EAAE;QACtE,OAAO0E,IAAI,CAACC,CAAC,CAAC;MAChB;MACA,IAAID,IAAI,CAACC,CAAC,CAAC,CAACjQ,QAAQ,EAAE;QACpB,IAAIgG,IAAI,GAAG0J,wBAAwB,CAACM,IAAI,CAACC,CAAC,CAAC,CAACjQ,QAAQ,EAAER,UAAU,EAAE8L,SAAS,CAAC;QAC5E,IAAItF,IAAI,EAAE;UACR,OAAOA,IAAI;QACb;MACF;IACF;EACJ;;EAEA;EACA;EACA,oBACE/G,OAAA,CAACX,OAAO;IAACuB,EAAE,EAAEA,EAAG;IAACqQ,SAAS,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAQ,CAAE;IAACC,kBAAkB,EAAE5I,wBAAyB;IAAClH,SAAS,EAAC,EAAE;IAAAP,QAAA,GACpHoI,YAAY,iBAAInJ,OAAA,CAACqR,mBAAmB;MAAA9P,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAEvC,CAACyH,YAAY,IAAIC,cAAc,CAACzI,MAAM,IAAI,CAAC,iBAAIX,OAAA,CAACb,WAAW;MAACmS,QAAQ;MAAAvQ,QAAA,EAAC;IAAO;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC,EAE1F,CAACyH,YAAY,IAAIC,cAAc,CAACzI,MAAM,GAAG,CAAC,IAAIyI,cAAc,CAACnI,GAAG,CAACsQ,EAAE,IAAI;MACtE,MAAMC,GAAG,GAAG,GAAGD,EAAE,CAAChR,UAAU,IAAIgR,EAAE,CAAClF,SAAS,EAAE;MAC9C,IAAI,CAAC5O,OAAO,CAAC8T,EAAE,CAACxQ,QAAQ,CAAC,EAAE;QACzB,oBAAOf,OAAA,CAAC8C,iBAAiB;UAAWJ,KAAK,eACvC1C,OAAA,CAAAE,SAAA;YAAAa,QAAA,gBACEf,OAAA;cAAMsB,SAAS,EAAE,WAAW,GAAGpD,gBAAgB,CAACqT,EAAE,CAAChR,UAAU,CAAC,GAAG;YAA8C;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvH1B,OAAA;cAAAe,QAAA,EAAOwQ,EAAE,CAACpQ;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,eACtB;UACF;UAAA;UACAqB,KAAK,eAAE/C,OAAA,CAAC5C,aAAa;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAX,QAAA,eAGzBf,OAAA;YAAKsB,SAAS,EAAEiQ,EAAE,CAAChR,UAAU,IAAIvC,UAAU,CAAC4N,aAAa,GAAG,0BAA0B,GAAG,EAAG;YAAA7K,QAAA,EACzFwQ,EAAE,CAAChR,UAAU,IAAIvC,UAAU,CAAC4N,aAAa;YACxC;YACA,CAAC,MAAM;cACL,MAAMxH,KAAK,GAAG,EAAE;cAChB,IAAIqN,iBAAiB,GAAG,EAAE;cAE1BF,EAAE,CAACxQ,QAAQ,CAACsD,OAAO,CAAC,CAACqN,GAAG,EAAEC,KAAK,KAAK;gBAClC,MAAMH,GAAG,GAAG,GAAGE,GAAG,CAACnR,UAAU,IAAImR,GAAG,CAACrF,SAAS,EAAE;;gBAEhD;gBACA,IAAIqF,GAAG,CAACtQ,IAAI,IAAIvD,cAAc,CAAC+T,MAAM,EAAE;kBACrC;kBACA,IAAIH,iBAAiB,CAAC9Q,MAAM,GAAG,CAAC,EAAE;oBAChCyD,KAAK,CAACyN,IAAI,cACR7R,OAAA;sBAAoCsB,SAAS,EAAC,mBAAmB;sBAAAP,QAAA,EAC9D0Q;oBAAiB,GADV,SAASE,KAAK,UAAU;sBAAApQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAE7B,CACP,CAAC;oBACD+P,iBAAiB,GAAG,EAAE;kBACxB;;kBAEA;kBACArN,KAAK,CAACyN,IAAI,cACR7R,OAAA;oBAAiBsB,SAAS,EAAC,iBAAiB;oBAAAP,QAAA,eAC1Cf,OAAA;sBAAKsB,SAAS,EAAC,aAAa;sBAAAP,QAAA,EAAE2Q,GAAG,CAACvQ;oBAAI;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC,GADrCiQ,KAAK;oBAAApQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACP,CAAC;gBACH,CAAC,MAAM;kBACL;kBACA+P,iBAAiB,CAACI,IAAI,cACpB7R,OAAA;oBAAiBsB,SAAS,EAAC,uCAAuC;oBAAAP,QAAA,eAChEf,OAAA,CAACb,WAAW;sBACVyB,EAAE,EAAE4Q,GAAI;sBACR7I,IAAI,EAAE+I,GAAI;sBACVJ,QAAQ,EAAEI,GAAG,CAACJ,QAAS;sBACvBnP,OAAO,EAAEA,OAAQ;sBAAApB,QAAA,GAGf2Q,GAAG,CAACnR,UAAU,IAAIvC,UAAU,CAAC4N,aAAa,gBAC1C5L,OAAA,CAAC1C,mBAAmB;wBAACqM,QAAQ,EAAE+H,GAAG,CAACrF,SAAU;wBAAC/K,SAAS,EAAC,4CAA4C;wBAACT,KAAK,EAAE;0BAACiR,UAAU,EAAE;wBAAM;sBAAE;wBAAAvQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,GACnIgQ,GAAG,CAACnR,UAAU,IAAIvC,UAAU,CAAC+T,sBAAsB,GAClD7T,gBAAgB,CAACwT,GAAG,CAACrF,SAAS,CAAC,iBAAIrM,OAAA;wBAAMsB,SAAS,EAAE,WAAW,GAAGpD,gBAAgB,CAACwT,GAAG,CAACrF,SAAS,CAAC,GAAG;sBAA8C;wBAAA9K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,gBAC3J1B,OAAA;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAEf1B,OAAA;wBAAKa,KAAK,EAAE;0BAAEsF,KAAK,EAAE;wBAAO,CAAE;wBAAApF,QAAA,EAAE2Q,GAAG,CAACvQ;sBAAI;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAG7C1B,OAAA;wBAAMsB,SAAS,EAAI,YAAYnD,iBAAiB,CAACuT,GAAG,CAAChB,UAAU,CAAC,CAACtO,IAAI,eAAgB;wBACpFvB,KAAK,EAAE;0BAAC8B,KAAK,EAAExE,iBAAiB,CAACuT,GAAG,CAAChB,UAAU,CAAC,CAACsB;wBAAS,CAAE;wBAC5DnQ,KAAK,EAAE6P,GAAG,CAAChB,UAAU,IAAIzS,WAAW,CAAC0S,yBAAyB,GAC1D,WAAW,GAAGe,GAAG,CAAChB,UAAU,IAAIzS,WAAW,CAAC4S,yBAAyB,GACpD,SAAS,GAAGa,GAAG,CAAChB,UAAU,IAAIzS,WAAW,CAAC6S,oBAAoB,GAC/C,OAAO,GAAG;sBAAG;wBAAAvP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C,CAAC,eAET1B,OAAA;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC,GA1BNiQ,KAAK;oBAAApQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA2BV,CACP,CAAC;gBACH;cACF,CAAC,CAAC;;cAEF;cACA,IAAI+P,iBAAiB,CAAC9Q,MAAM,GAAG,CAAC,EAAE;gBAChCyD,KAAK,CAACyN,IAAI,cACR7R,OAAA;kBAA8BsB,SAAS,EAAC,mBAAmB;kBAAAP,QAAA,EACxD0Q;gBAAiB,GADX,oBAAoB;kBAAAlQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAExB,CACP,CAAC;cACH;cAEA,OAAO0C,KAAK;YACd,CAAC,EAAE,CAAC;YAEJ;YACAmN,EAAE,CAACxQ,QAAQ,CAACE,GAAG,CAAC,CAACyQ,GAAG,EAAEC,KAAK,KAAK;cAC9B,MAAMH,GAAG,GAAG,GAAGE,GAAG,CAACnR,UAAU,IAAImR,GAAG,CAACrF,SAAS,EAAE;cAChD,oBACErM,OAAA;gBAAiBsB,SAAS,EAAC,EAAE;gBAAAP,QAAA,eAC3Bf,OAAA,CAACb,WAAW;kBACVyB,EAAE,EAAE4Q,GAAI;kBACR7I,IAAI,EAAE+I,GAAI;kBACVJ,QAAQ,EAAEI,GAAG,CAACJ,QAAS;kBACvBnP,OAAO,EACLuP,GAAG,CAACnR,UAAU,IAAIvC,UAAU,CAACmP,qBAAqB,IAClDuE,GAAG,CAACnR,UAAU,IAAIvC,UAAU,CAAC4P,eAAe,IAC5C8D,GAAG,CAACnR,UAAU,IAAIvC,UAAU,CAACgR,eAAe,IAC5C0C,GAAG,CAACnR,UAAU,IAAIvC,UAAU,CAACiQ,gBAAgB,IAC7CyD,GAAG,CAACnR,UAAU,IAAIvC,UAAU,CAACsR,iBAAiB,IAC9CoC,GAAG,CAACnR,UAAU,IAAIvC,UAAU,CAACyR,eAAe,GACxC,MAAM,CAAC,CAAC,GAAGtN,OAChB;kBACDtB,KAAK,EAAE6Q,GAAG,CAACtQ,IAAI,IAAIvD,cAAc,CAAC+T,MAAM,GAAG;oBAAEK,WAAW,EAAE;kBAAO,CAAC,GAAG,CAAC,CAAE;kBACxE3Q,SAAS,EAAIoQ,GAAG,CAACnR,UAAU,IAAIvC,UAAU,CAACmP,qBAAqB,IAC7DuE,GAAG,CAACnR,UAAU,IAAIvC,UAAU,CAAC4P,eAAe,IAC5C8D,GAAG,CAACnR,UAAU,IAAIvC,UAAU,CAACgR,eAAe,IAC5C0C,GAAG,CAACnR,UAAU,IAAIvC,UAAU,CAACiQ,gBAAgB,IAC7CyD,GAAG,CAACnR,UAAU,IAAIvC,UAAU,CAACsR,iBAAiB,IAC9CoC,GAAG,CAACnR,UAAU,IAAIvC,UAAU,CAACyR,eAAe,GACxC,wBAAwB,GAAG,EAAI;kBAAA1O,QAAA,GAGnC2Q,GAAG,CAACnR,UAAU,IAAIvC,UAAU,CAAC4N,aAAa,gBAC1C5L,OAAA,CAAC1C,mBAAmB;oBAACqM,QAAQ,EAAE+H,GAAG,CAACrF,SAAU;oBAAC/K,SAAS,EAAC,4CAA4C;oBAACT,KAAK,EAAE;sBAACiR,UAAU,EAAE;oBAAM;kBAAE;oBAAAvQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,GACnIgQ,GAAG,CAACnR,UAAU,IAAIvC,UAAU,CAAC+T,sBAAsB,GAClD7T,gBAAgB,CAACwT,GAAG,CAACrF,SAAS,CAAC,iBAAIrM,OAAA;oBAAMsB,SAAS,EAAE,WAAW,GAAGpD,gBAAgB,CAACwT,GAAG,CAACrF,SAAS,CAAC,GAAG;kBAA8C;oBAAA9K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,gBAC3J1B,OAAA;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAEf1B,OAAA;oBAAKa,KAAK,EAAE;sBAAEsF,KAAK,EAAE;oBAAO,CAAE;oBAAApF,QAAA,EAAE2Q,GAAG,CAACvQ;kBAAI;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAG7C1B,OAAA;oBAAMsB,SAAS,EAAI,YAAYnD,iBAAiB,CAACuT,GAAG,CAAChB,UAAU,CAAC,CAACtO,IAAI,eAAgB;oBACpFvB,KAAK,EAAE;sBAAC8B,KAAK,EAAExE,iBAAiB,CAACuT,GAAG,CAAChB,UAAU,CAAC,CAACsB;oBAAS,CAAE;oBAC5DnQ,KAAK,EAAE6P,GAAG,CAAChB,UAAU,IAAIzS,WAAW,CAAC0S,yBAAyB,GAC1D,WAAW,GAAGe,GAAG,CAAChB,UAAU,IAAIzS,WAAW,CAAC4S,yBAAyB,GACpD,SAAS,GAAGa,GAAG,CAAChB,UAAU,IAAIzS,WAAW,CAAC6S,oBAAoB,GAC/C,OAAO,GAAG;kBAAG;oBAAAvP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eAET1B,OAAA;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC,GA1CNiQ,KAAK;gBAAApQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA2CV,CAAC;YAEV,CAAC;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GAvIuB8P,GAAG;UAAAjQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwIf,CAAC;MACtB,CAAC,MAAM;QACL,oBAAO1B,OAAA,CAACb,WAAW;UAAWyB,EAAE,EAAE4Q,GAAI;UAACrP,OAAO,EAAEA,OAAQ;UAAApB,QAAA,GACrDwQ,EAAE,CAAChR,UAAU,IAAI,EAAE,gBACpBP,OAAA;YAAMsB,SAAS,EAAE,WAAW,GAAGpD,gBAAgB,CAACqT,EAAE,CAAChR,UAAU,CAAC,GAAG,+BAAgC;YAACM,KAAK,EAAE;cAACqR,UAAU,EAAC,CAAC,CAAC;cAACC,WAAW,EAAC;YAAC;UAAE;YAAA5Q,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAE/I1B,OAAA;YAAMsB,SAAS,EAAE,WAAW,GAAGpD,gBAAgB,CAACqT,EAAE,CAAChR,UAAU,CAAC,GAAG;UAA8C;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAEvH1B,OAAA;YAAKa,KAAK,EAAE;cAAEsF,KAAK,EAAE;YAAO,CAAE;YAAApF,QAAA,EAAEwQ,EAAE,CAACpQ;UAAI;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GANvB8P,GAAG;UAAAjQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOf,CAAC;MAChB;IACF,CAAC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEK,CAAC;AAEd;AAACgH,GAAA,CAtfQN,YAAY;EAAA,QAIf7K,qCAAqC,EACTC,mCAAmC,EAEGmC,WAAW,EAItDA,WAAW,EA0BrBH,cAAc;AAAA;AAAA4S,GAAA,GArCxBhK,YAAY;AAyfrB,SAASiJ,mBAAmBA,CAAA,EAAG;EAC7B,oBAAOrR,OAAA,CAACpB,KAAK,CAACqB,QAAQ;IAAAc,QAAA,gBACpBf,OAAA,CAACb,WAAW;MAACmS,QAAQ;MAAAvQ,QAAA,eACnBf,OAAA,CAACtB,QAAQ,CAAC2T,KAAK;QAACC,MAAM,EAAE,IAAK;QAACzR,KAAK,EAAE;UAACwE,MAAM,EAAC,MAAM;UAACkN,QAAQ,EAAC;QAAQ;MAAE;QAAAhR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,eACd1B,OAAA,CAACb,WAAW;MAACmS,QAAQ;MAAAvQ,QAAA,eACnBf,OAAA,CAACtB,QAAQ,CAAC2T,KAAK;QAACC,MAAM,EAAE,IAAK;QAACzR,KAAK,EAAE;UAACwE,MAAM,EAAC,MAAM;UAACkN,QAAQ,EAAC;QAAQ;MAAE;QAAAhR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,eACd1B,OAAA,CAACb,WAAW;MAACmS,QAAQ;MAAAvQ,QAAA,eACnBf,OAAA,CAACtB,QAAQ,CAAC2T,KAAK;QAACC,MAAM,EAAE,IAAK;QAACzR,KAAK,EAAE;UAACwE,MAAM,EAAC,MAAM;UAACkN,QAAQ,EAAC;QAAQ;MAAE;QAAAhR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,eACd1B,OAAA,CAACb,WAAW;MAACmS,QAAQ;MAAAvQ,QAAA,eACnBf,OAAA,CAACtB,QAAQ,CAAC2T,KAAK;QAACC,MAAM,EAAE,IAAK;QAACzR,KAAK,EAAE;UAACwE,MAAM,EAAC,MAAM;UAACkN,QAAQ,EAAC;QAAQ;MAAE;QAAAhR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,eACd1B,OAAA,CAACb,WAAW;MAACmS,QAAQ;MAAAvQ,QAAA,eACnBf,OAAA,CAACtB,QAAQ,CAAC2T,KAAK;QAACC,MAAM,EAAE,IAAK;QAACzR,KAAK,EAAE;UAACwE,MAAM,EAAC,MAAM;UAACkN,QAAQ,EAAC;QAAQ;MAAE;QAAAhR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AACnB;AAAC8Q,GAAA,GAlBQnB,mBAAmB;AAqB5B,eAAAoB,GAAA,gBAAe5T,UAAU,CAACuJ,YAAY,CAAC;AAAA,IAAAzG,EAAA,EAAAG,GAAA,EAAAO,GAAA,EAAAE,GAAA,EAAA4F,GAAA,EAAAiK,GAAA,EAAAI,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAA/Q,EAAA;AAAA+Q,YAAA,CAAA5Q,GAAA;AAAA4Q,YAAA,CAAArQ,GAAA;AAAAqQ,YAAA,CAAAnQ,GAAA;AAAAmQ,YAAA,CAAAvK,GAAA;AAAAuK,YAAA,CAAAN,GAAA;AAAAM,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}